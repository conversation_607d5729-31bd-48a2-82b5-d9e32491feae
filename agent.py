"""
Agent module for the Advanced AI Agent.
"""

import re
import json
import os
import time
from pathlib import Path
from typing import Dict, List, Optional, Any, Union, Tuple, Generator, Callable

from models import ModelManager
from conversation import Conversation, Message, ConversationManager
from tools import (
    ShellTool, FileTool, CodeTool, WebTool, CodebaseTool, VisionTool,
    PatchTool, BrowserTool, RagTool, SearchAPI,
    WebScraperTool, InformationSynthesizer, WebInfoManager
)
from tools.threading.thread_manager import ThreadManager
from tools.predictive.prefetcher import PredictivePrefetcher
from tools.predictive.context_analyzer import ContextAnalyzer
from tools.predictive.code_predictor import CodePredictor
from core.enhanced_agent import EnhancedAgent, AgentMode, TaskType
from core.intelligent_refactoring import IntelligentRefactoring
from core.iterative_execution_manager import IterativeExecutionManager
from tools.tool_manager import Tool, ToolManager
from core.ai_code_assistant import AICodeAssistant, AssistantRequest, AssistantResponse
from utils import get_logger

# Import additional advanced components
from core.adaptive_controller import Adapt<PERSON><PERSON><PERSON>roller
from core.advanced_cache_system import AdvancedCacheSystem
from core.performance_analyzer import <PERSON>Analyzer
from core.error_detector import ErrorDetector
from core.learning_system import LearningSystem

# Get the logger
logger = get_logger()

# Simple implementations for predictive features
class SimplePredictivePrefetcher:
    """Simple predictive prefetcher implementation."""

    def predict_next_actions(self, message: str, context: Dict[str, Any]) -> List[str]:
        """Predict next actions based on message and context."""
        suggestions = []
        message_lower = message.lower()

        if 'file' in message_lower:
            suggestions.extend(['read related files', 'check file permissions', 'backup files'])
        if 'code' in message_lower:
            suggestions.extend(['analyze dependencies', 'check syntax', 'run tests'])
        if 'web' in message_lower:
            suggestions.extend(['check connectivity', 'validate URLs', 'cache responses'])
        if 'search' in message_lower:
            suggestions.extend(['index content', 'prepare filters', 'cache results'])
        
        return suggestions[:5]  # Return top 5 suggestions

class SimpleContextAnalyzer:
    """Simple context analyzer implementation."""

    def analyze_context(self, message: str, conversation: Any) -> Dict[str, Any]:
        """Analyze context of the message and conversation."""
        analysis = {
            'complexity_score': 0.5,
            'complexity_level': 'standard',
            'involves_code': False,
            'involves_files': False,
            'involves_web': False,
            'estimated_time': 30
        }

        message_lower = message.lower()

        # Analyze complexity
        complexity_indicators = {
            'simple': ['list', 'show', 'get', 'display'],
            'standard': ['create', 'write', 'read', 'search'],
            'complex': ['analyze', 'optimize', 'refactor', 'debug'],
            'advanced': ['integrate', 'deploy', 'architect']
        }

        for level, keywords in complexity_indicators.items():
            if any(keyword in message_lower for keyword in keywords):
                analysis['complexity_level'] = level
                analysis['complexity_score'] = {
                    'simple': 0.2, 'standard': 0.5, 'complex': 0.8, 'advanced': 1.0
                }.get(level, 0.5)
                break

        # Check what's involved
        analysis['involves_code'] = any(word in message_lower for word in ['code', 'execute', 'run', 'compile'])
        analysis['involves_files'] = any(word in message_lower for word in ['file', 'read', 'write', 'save'])
        analysis['involves_web'] = any(word in message_lower for word in ['web', 'url', 'http', 'website'])

        # Estimate time
        time_factors = sum([
            analysis['complexity_score'] * 60,
            30 if analysis['involves_code'] else 0,
            10 if analysis['involves_files'] else 0,
            20 if analysis['involves_web'] else 0
        ])
        analysis['estimated_time'] = max(10, int(time_factors))

        return analysis

class SimpleCodePredictor:
    """Simple code predictor implementation."""

    def predict_code_completion(self, message: str, context: Dict[str, Any]) -> Dict[str, Any]:
        """Predict code completion based on message and context."""
        prediction = {
            'suggestion': '',
            'confidence': 0.6,
            'language': 'python'
        }

        message_lower = message.lower()

        if 'python' in message_lower:
            prediction['language'] = 'python'
            if 'function' in message_lower:
                prediction['suggestion'] = 'Consider using proper function documentation and type hints'
            elif 'class' in message_lower:
                prediction['suggestion'] = 'Consider using dataclasses or proper inheritance patterns'
            elif 'loop' in message_lower:
                prediction['suggestion'] = 'Consider using list comprehensions or generator expressions for efficiency'
        elif 'javascript' in message_lower:
            prediction['language'] = 'javascript'
            prediction['suggestion'] = 'Consider using modern ES6+ features and proper error handling'
        elif 'java' in message_lower:
            prediction['language'] = 'java'
            prediction['suggestion'] = 'Consider using proper exception handling and design patterns'

        return prediction

class Agent:
    """The AI agent."""

    def __init__(
        self,
        model_manager: ModelManager,
        conversation_manager: ConversationManager,
        workspace_dir: Optional[Path] = None,
        system_prompt: Optional[str] = None
    ):
        """Initialize the agent.

        Args:
            model_manager: The model manager to use.
            conversation_manager: The conversation manager to use.
            workspace_dir: The workspace directory to use. If None, will use the current directory.
            system_prompt: The system prompt to use. If None, will use a default prompt.
        """
        self.model_manager = model_manager
        self.conversation_manager = conversation_manager
        self.workspace_dir = workspace_dir or Path.cwd()
        self._has_rag = False  # Initialize _has_rag attribute

        # Initialize tool manager
        self.tool_manager = ToolManager()

        # Initialize tools first, then set the system prompt
        self._initialize_tools()

        # Initialize AI Code Assistant
        self.ai_code_assistant = AICodeAssistant(self.model_manager, self.workspace_dir)

        # Initialize advanced components
        self.adaptive_controller = AdaptiveController(model_manager=self.model_manager, workspace_dir=self.workspace_dir)
        self.cache_system = AdvancedCacheSystem()
        self.performance_analyzer = PerformanceAnalyzer(workspace_dir=self.workspace_dir)
        self.error_detector = ErrorDetector(workspace_dir=self.workspace_dir)
        self.learning_system = LearningSystem(workspace_dir=self.workspace_dir)

        # Initialize iterative execution manager
        self.iterative_manager = IterativeExecutionManager(model_manager=self.model_manager)

        # Initialize thread manager for multi-threaded execution
        self.thread_manager = ThreadManager(max_workers=4, enable_monitoring=True)

        # Initialize predictive prefetcher with simplified constructor
        self.prefetcher = SimplePredictivePrefetcher()

        # Initialize context analyzer
        self.context_analyzer = SimpleContextAnalyzer()

        # Initialize code predictor
        self.code_predictor = SimpleCodePredictor()

        # Initialize iterative mode flag
        self.iterative_mode = False

        # Create tools dictionary for backward compatibility
        self.tools = {}
        self._populate_tools_dict()

        # Set the system prompt
        if system_prompt is None:
            self.system_prompt = self._get_default_system_prompt()
        else:
            self.system_prompt = system_prompt

    def _initialize_tools(self):
        """Initialize and register the tools with the ToolManager."""
        # Initialize individual tool instances
        self.shell_tool = ShellTool(self.workspace_dir)
        self.file_tool = FileTool(self.workspace_dir)
        self.code_tool = CodeTool()
        self.web_tool = WebTool()
        self.codebase_tool = CodebaseTool(self.workspace_dir)
        self.vision_tool = VisionTool(self.workspace_dir)
        self.patch_tool = PatchTool(self.workspace_dir)
        self.browser_tool = BrowserTool()
        # Initialize search_api first since it's used by other tools
        self.search_api = SearchAPI()
        self.web_scraper = WebScraperTool()
        self.info_synthesizer = InformationSynthesizer()
        self.web_info_manager = WebInfoManager()

        # Initialize RAG tool if dependencies are available
        try:
            import importlib.util
            has_faiss = importlib.util.find_spec("faiss") is not None
            has_sentence_transformers = importlib.util.find_spec("sentence_transformers") is not None

            if has_faiss and has_sentence_transformers:
                self.rag_tool = RagTool(self.workspace_dir)
                self._has_rag = True
            else:
                self._has_rag = False
        except Exception:
            self._has_rag = False

        # Register tools with the ToolManager
        self._register_shell_tools()
        self._register_file_tools()
        self._register_code_tools()
        self._register_web_tools()
        self._register_codebase_tools()
        self._register_vision_tools()
        self._register_patch_tools()
        self._register_browser_tools()
        self._register_scrape_tools()
        self._register_info_tools()
        self._register_ai_assistant_tool()
        self._register_search_tools()
        if self._has_rag:
            self._register_rag_tool()

    def _populate_tools_dict(self):
        """Populate the tools dictionary for backward compatibility."""
        # Map tool names to their execution functions
        self.tools = {
            'shell': self._execute_shell,
            'file': self._execute_file_operation,
            'code': self._execute_code_operation,
            'web': self._execute_web_operation,
            'codebase': self._execute_codebase_operation,
            'vision': self._execute_vision_operation,
            'patch': self._execute_patch_operation,
            'browser': self._execute_browser_operation,
            'search': self._execute_search_operation,
            'scrape': self._execute_scrape_operation,
            'info': self._execute_info_operation,
            'ai_assistant': self._execute_ai_assistant_operation,
        }
        if self._has_rag:
            self.tools['rag'] = self._execute_rag_operation

    def _register_shell_tools(self):
        self.tool_manager.register_tool(Tool(
            name="shell",
            description="Execute shell commands on the system.",
            function=self._execute_shell,
            parameters={
                "type": "object",
                "properties": {
                    "command": {"type": "string", "description": "The shell command to execute."}
                },
                "required": ["command"]
            }
        ))

    def _execute_shell(self, args: str) -> str:
        """Execute a shell command.

        Args:
            args: The command to execute.

        Returns:
            The command output or error message.
        """
        if not args.strip():
            return "Error: No command specified."

        try:
            stdout, stderr, return_code = self.shell_tool.execute(args)

            if return_code == 0:
                if stdout:
                    return f"Command executed successfully:\n\n{stdout}"
                return "Command executed successfully (no output)"
            else:
                return f"Command failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing command: {e}"

    def _execute_file_operation(self, args: str) -> str:
        """Execute file operations using the file tool."""
        return self._execute_file(args)

    def _execute_code_operation(self, args: str) -> str:
        """Execute code operations using the code tool."""
        return self._execute_code(args)

    def _execute_web_operation(self, args: str) -> str:
        """Execute web operations using the web tool."""
        return self._execute_web(args)

    def _execute_codebase_operation(self, args: str) -> str:
        """Execute codebase operations using the codebase tool."""
        return self._execute_codebase(args)

    def _execute_vision_operation(self, args: str) -> str:
        """Execute vision operations using the vision tool."""
        return self._execute_vision(args)

    def _execute_patch_operation(self, args: str) -> str:
        """Execute patch operations using the patch tool."""
        return self._execute_patch(args)

    def _execute_browser_operation(self, args: str) -> str:
        """Execute browser operations using the browser tool."""
        return self._execute_browser_read_url(args)

    def _execute_search_operation(self, args: str) -> str:
        """Execute search operations using the search tool."""
        return self._execute_search_web(args)

    def _execute_scrape_operation(self, args: str) -> str:
        """Execute scrape operations using the scrape tool."""
        return self._execute_scrape_web(args)

    def _execute_info_operation(self, args: str) -> str:
        """Execute info operations using the info tool."""
        return self._execute_info_retrieve(args)

    def _execute_ai_assistant_operation(self, args: str) -> str:
        """Execute AI assistant operations."""
        return self._execute_ai_assistant(args)

    def _execute_rag_operation(self, args: str) -> str:
        """Execute RAG operations."""
        return self._execute_rag(args)

    def _register_file_tools(self):
        """Register file-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="file_read",
            description="Read the content of a file.",
            function=self._file_read,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to read."}
                },
                "required": ["path"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_write",
            description="Write content to a file. Overwrites if the file exists.",
            function=self._file_write,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to write."},
                    "content": {"type": "string", "description": "The content to write to the file."}
                },
                "required": ["path", "content"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_append",
            description="Append content to an existing file.",
            function=self._file_append,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to append to."},
                    "content": {"type": "string", "description": "The content to append to the file."}
                },
                "required": ["path", "content"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_delete",
            description="Delete a file.",
            function=self._file_delete,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the file to delete."}
                },
                "required": ["path"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_list",
            description="List files and directories in a given path.",
            function=self._file_list,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The directory path to list. Defaults to current directory."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_search",
            description="Search for files matching a pattern.",
            function=self._file_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'report.txt')."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="file_grep",
            description="Search for a pattern within files.",
            function=self._file_grep,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.py'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))

    def _register_code_tools(self):
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _register_web_tools(self):
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _execute_web_search(self, query: str) -> str:
        """Execute a web search operation.

        Args:
            query: The search query

        Returns:
            Search results in JSON format
        """
        try:
            results = self.web_tool.search(query)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error performing web search: {str(e)}"

    def _execute_web_fetch(self, url: str) -> str:
        """Fetch content from a URL.

        Args:
            url: The URL to fetch

        Returns:
            Fetched content with metadata
        """
        try:
            content, metadata = self.web_tool.fetch_url(url)
            return (
                f"URL: {url}\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars
            )
        except Exception as e:
            return f"Error fetching URL: {str(e)}"

    def _register_codebase_tools(self):
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_find_code_files",
            description="Find all code files in the codebase.",
            function=self._execute_codebase_find_code_files,
            parameters={"type": "object", "properties": {}, "required": []}
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_analyze",
            description="Analyze a specific code file for structure and potential issues.",
            function=self._execute_codebase_analyze,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the code file to analyze."}
                },
                "required": ["path"]
            }
        ))

    def _execute_codebase_find_files(self, pattern: str = "*") -> str:
        """Find files in the codebase matching a pattern.

        Args:
            pattern: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self) -> str:
        """Find all code files in the codebase.

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within code files.

        Args:
            pattern: The regex pattern to search for.
            file_pattern: Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."

        Returns:
            Search results with matching lines and context.
        """
        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, path: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            path: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not path.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(path)
            if analysis:
                formatted_analysis = {
                    "file": path,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {path}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {path}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _register_vision_tools(self):
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="vision_load_image",
            description="Load an image from a given path.",
            function=self._execute_vision_load_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the image file."}
                },
                "required": ["path"]
            }
        ))

    def _execute_vision_take_screenshot(self, path: str = None) -> str:
        """Take a screenshot of the current screen.

        Args:
            path: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, path: str) -> str:
        """Load an image from a given path.

        Args:
            path: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not path.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(path)
            return (
                f"Image loaded successfully:\n"
                f"Path: {path}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"

    def _register_patch_tools(self):
        """Register patch-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _register_browser_tools(self):
        """Register browser-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The language and code to execute separated by a delimiter.

        Returns:
            The execution result or error message.
        """
        # Parse the arguments (language and code)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both language and code must be specified."

        language = args_parts[0].lower()
        code = args_parts[1]

        try:
            # Execute the code
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse file path and content
            parts = args_parts[1].split(maxsplit=1)
            if len(parts) < 2:
                return "Error: No file content specified."

            file_path = parts[0]
            content = parts[1]

            try:
                self.file_tool.write(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files
            path = args_parts[1] if len(args_parts) > 1 else "."

            try:
                files = self.file_tool.list(path)
                return f"Files in {path}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}"

    def _file_read(self, path: str) -> str:
        """Read the content of a file."""
        try:
            return self.file_tool.read(path)
        except Exception as e:
            return f"Error reading file: {e}"

    def _file_write(self, path: str, content: str) -> str:
        """Write content to a file. Overwrites if the file exists."""
        try:
            self.file_tool.write(path, content)
            return f"File written successfully: {path}"
        except Exception as e:
            return f"Error writing file: {e}"

    def _file_append(self, path: str, content: str) -> str:
        """Append content to an existing file."""
        try:
            self.file_tool.append(path, content)
            return f"Content appended successfully to: {path}"
        except Exception as e:
            return f"Error appending to file: {e}"

    def _file_delete(self, path: str) -> str:
        """Delete a file."""
        try:
            self.file_tool.delete(path)
            return f"File deleted successfully: {path}"
        except Exception as e:
            return f"Error deleting file: {e}"

    def _file_list(self, path: str = ".") -> str:
        """List files and directories in a given path."""
        try:
            files = self.file_tool.list(path)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error listing files: {e}"

    def _file_search(self, pattern: str) -> str:
        """Search for files matching a pattern."""
        try:
            files = self.file_tool.search(pattern)
            return json.dumps(files, indent=2)
        except Exception as e:
            return f"Error searching for files: {e}"

    def _file_grep(self, pattern: str, file_pattern: str = "*") -> str:
        """Search for a pattern within files."""
        try:
            results = self.file_tool.grep(pattern, file_pattern)
            return json.dumps(results, indent=2)
        except Exception as e:
            return f"Error grepping files: {e}"

    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="code_execute",
            description="Execute code in a specified programming language.",
            function=self._execute_code,
            parameters={
                "type": "object",
                "properties": {
                    "language": {"type": "string", "description": "The programming language (e.g., 'python', 'javascript')."},
                    "code": {"type": "string", "description": "The code to execute."}
                },
                "required": ["language", "code"]
            }
        ))

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The language and code to execute separated by a delimiter.

        Returns:
            The execution result or error message.
        """
        # Parse the arguments (language and code)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both language and code must be specified."

        language = args_parts[0].lower()
        code = args_parts[1]

        try:
            # Execute the code
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def _register_web_tools(self):
        self.tool_manager.register_tool(Tool(
            name="web_search",
            description="Search the web for information.",
            function=self._execute_web_search,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."}
                },
                "required": ["query"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="web_fetch",
            description="Fetch content from a given URL.",
            function=self._execute_web_fetch,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to fetch."}
                },
                "required": ["url"]
            }
        ))

    def _execute_web_search(self, args: str) -> str:
        """Execute a web search operation.

        Args:
            args: The search query.

        Returns:
            The search results.
        """
        if not args.strip():
            return "Error: No search query specified."

        try:
            results = self.web_tool.search(args)
            if results:
                return f"Web search results for '{args}':\n\n{json.dumps(results, indent=2)}"
            else:
                return "No results found for the search query."
        except Exception as e:
            return f"Error performing web search: {e}"

    def _execute_web_fetch(self, args: str) -> str:
        """Fetch content from a URL.

        Args:
            args: The URL to fetch.

        Returns:
            The fetched content.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.web_tool.fetch_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error fetching URL: {e}"

    def _execute_web_search(self, args: str) -> str:
        """Execute a web search operation.

        Args:
            args: The search query.

        Returns:
            The search results.
        """
        if not args.strip():
            return "Error: No search query specified."

        try:
            results = self.web_tool.search(args)
            if results:
                return f"Web search results for '{args}':\n\n{json.dumps(results, indent=2)}"
            else:
                return "No results found for the search query."
        except Exception as e:
            return f"Error performing web search: {e}"

    def _execute_web_fetch(self, args: str) -> str:
        """Fetch content from a URL.

        Args:
            args: The URL to fetch.

        Returns:
            The fetched content.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.web_tool.fetch_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error fetching URL: {e}"

    def _register_codebase_tools(self):
        self.tool_manager.register_tool(Tool(
            name="codebase_find_files",
            description="Find files in the codebase matching a pattern.",
            function=self._execute_codebase_find_files,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The pattern to search for (e.g., '*.py', 'config.json'). Defaults to '*'."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_find_code_files",
            description="Find all code files in the codebase.",
            function=self._execute_codebase_find_code_files,
            parameters={"type": "object", "properties": {}, "required": []}
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_search",
            description="Search for a pattern within code files.",
            function=self._execute_codebase_search,
            parameters={
                "type": "object",
                "properties": {
                    "pattern": {"type": "string", "description": "The regex pattern to search for."},
                    "file_pattern": {"type": "string", "description": "Optional glob pattern to filter files (e.g., '*.js'). Defaults to '*'."}
                },
                "required": ["pattern"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="codebase_analyze",
            description="Analyze a specific code file for structure and potential issues.",
            function=self._execute_codebase_analyze,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the code file to analyze."}
                },
                "required": ["path"]
            }
        ))

    def _execute_codebase_find_files(self, args: str) -> str:
        """Find files in the codebase matching a pattern.

        Args:
            args: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        pattern = args.strip() if args.strip() else "*"

        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self, args: str) -> str:
        """Find all code files in the codebase.

        Args:
            args: Unused parameter (kept for consistency with other tool methods).

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, args: str) -> str:
        """Search for a pattern within code files.

        Args:
            args: The search arguments containing pattern and optional file pattern.

        Returns:
            Search results with matching lines and context.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search pattern specified."

        pattern = args_parts[0]
        file_pattern = args_parts[1] if len(args_parts) > 1 else "*"

        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, args: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            args: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not args.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(args)
            if analysis:
                formatted_analysis = {
                    "file": args,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {args}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {args}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _execute_codebase_find_files(self, args: str) -> str:
        """Find files in the codebase matching a pattern.

        Args:
            args: The pattern to search for (e.g., '*.py'). If empty, searches for all files.

        Returns:
            List of matching files with their paths.
        """
        pattern = args.strip() if args.strip() else "*"

        try:
            files = self.codebase_tool.find_files(pattern)
            if files:
                return f"Found {len(files)} files matching '{pattern}':\n\n{json.dumps(files, indent=2)}"
            else:
                return f"No files found matching pattern: {pattern}"
        except Exception as e:
            return f"Error finding files: {e}"

    def _execute_codebase_find_code_files(self, args: str) -> str:
        """Find all code files in the codebase.

        Args:
            args: Unused parameter (kept for consistency with other tool methods).

        Returns:
            List of code files with their paths.
        """
        try:
            files = self.codebase_tool.find_code_files()
            if files:
                return f"Found {len(files)} code files:\n\n{json.dumps(files, indent=2)}"
            else:
                return "No code files found in the codebase."
        except Exception as e:
            return f"Error finding code files: {e}"

    def _execute_codebase_search(self, args: str) -> str:
        """Search for a pattern within code files.

        Args:
            args: The search arguments containing pattern and optional file pattern.

        Returns:
            Search results with matching lines and context.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search pattern specified."

        pattern = args_parts[0]
        file_pattern = args_parts[1] if len(args_parts) > 1 else "*"

        try:
            results = self.codebase_tool.search(pattern, file_pattern)
            if results:
                formatted_results = []
                for result in results:
                    formatted_results.append({
                        "file": result["file"],
                        "matches": [
                            {
                                "line": match["line"],
                                "content": match["content"].strip(),
                                "context": [line.strip() for line in match["context"]]
                            }
                            for match in result["matches"]
                        ]
                    })
                return f"Search results for '{pattern}' in '{file_pattern}':\n\n{json.dumps(formatted_results, indent=2)}"
            else:
                return f"No matches found for pattern '{pattern}' in files matching '{file_pattern}'"
        except Exception as e:
            return f"Error searching codebase: {e}"

    def _execute_codebase_analyze(self, args: str) -> str:
        """Analyze a specific code file for structure and potential issues.

        Args:
            args: The path to the code file to analyze.

        Returns:
            Analysis results including structure, dependencies, and potential issues.
        """
        if not args.strip():
            return "Error: No file path specified."

        try:
            analysis = self.codebase_tool.analyze(args)
            if analysis:
                formatted_analysis = {
                    "file": args,
                    "language": analysis.get("language", "unknown"),
                    "structure": analysis.get("structure", []),
                    "dependencies": analysis.get("dependencies", []),
                    "issues": analysis.get("issues", []),
                    "metrics": analysis.get("metrics", {})
                }
                return f"Analysis results for {args}:\n\n{json.dumps(formatted_analysis, indent=2)}"
            else:
                return f"No analysis results available for {args}"
        except Exception as e:
            return f"Error analyzing file: {e}"

    def _register_vision_tools(self):
        self.tool_manager.register_tool(Tool(
            name="vision_take_screenshot",
            description="Take a screenshot of the current screen.",
            function=self._execute_vision_take_screenshot,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "Optional path to save the screenshot. Defaults to a temporary file."}
                },
                "required": []
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="vision_load_image",
            description="Load an image from a given path.",
            function=self._execute_vision_load_image,
            parameters={
                "type": "object",
                "properties": {
                    "path": {"type": "string", "description": "The path to the image file."}
                },
                "required": ["path"]
            }
        ))

    def _execute_vision_take_screenshot(self, args: str) -> str:
        """Take a screenshot of the current screen.

        Args:
            args: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        path = args.strip() if args.strip() else None

        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, args: str) -> str:
        """Load an image from a given path.

        Args:
            args: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not args.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(args)
            return (
                f"Image loaded successfully:\n"
                f"Path: {args}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"

    def _execute_vision_take_screenshot(self, args: str) -> str:
        """Take a screenshot of the current screen.

        Args:
            args: Optional path to save the screenshot. If empty, uses a temporary file.

        Returns:
            Path where the screenshot was saved.
        """
        path = args.strip() if args.strip() else None

        try:
            screenshot_path = self.vision_tool.take_screenshot(path)
            return f"Screenshot saved to: {screenshot_path}"
        except Exception as e:
            return f"Error taking screenshot: {e}"

    def _execute_vision_load_image(self, args: str) -> str:
        """Load an image from a given path.

        Args:
            args: The path to the image file to load.

        Returns:
            Image metadata including dimensions and format.
        """
        if not args.strip():
            return "Error: No image path specified."

        try:
            image = self.vision_tool.load_image(args)
            return (
                f"Image loaded successfully:\n"
                f"Path: {args}\n"
                f"Dimensions: {image.width}x{image.height}\n"
                f"Format: {image.format}"
            )
        except Exception as e:
            return f"Error loading image: {e}"



    def _register_patch_tools(self):
        self.tool_manager.register_tool(Tool(
            name="patch_apply",
            description="Apply a patch to a file.",
            function=self._execute_patch_apply,
            parameters={
                "type": "object",
                "properties": {
                    "file_path": {"type": "string", "description": "The path to the file to patch."},
                    "original_code": {"type": "string", "description": "The original code snippet to replace."},
                    "updated_code": {"type": "string", "description": "The new code snippet to insert."}
                },
                "required": ["file_path", "original_code", "updated_code"]
            }
        ))

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _execute_patch_apply(self, args: str) -> str:
        """Apply a patch to a file.

        Args:
            args: The arguments containing file path, original code, and updated code.

        Returns:
            Confirmation of the patch being applied.
        """
        # Parse the arguments (file_path, original_code, updated_code)
        args_parts = args.strip().split(maxsplit=2)
        if len(args_parts) < 3:
            return "Error: All three arguments (file_path, original_code, updated_code) must be specified."

        file_path = args_parts[0]
        original_code = args_parts[1]
        updated_code = args_parts[2]

        try:
            success = self.patch_tool.apply(file_path, original_code, updated_code)
            if success:
                return f"Patch successfully applied to {file_path}"
            else:
                return f"Failed to apply patch to {file_path}"
        except Exception as e:
            return f"Error applying patch: {e}"

    def _register_browser_tools(self):
        self.tool_manager.register_tool(Tool(
            name="browser_read_url",
            description="Browse a URL and get its content.",
            function=self._execute_browser_read_url,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to browse."}
                },
                "required": ["url"]
            }
        ))

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _execute_browser_read_url(self, args: str) -> str:
        """Browse a URL and get its content.

        Args:
            args: The URL to browse.

        Returns:
            The content of the URL with metadata.
        """
        if not args.strip():
            return "Error: No URL specified."

        try:
            content, metadata = self.browser_tool.read_url(args)
            return (
                f"Content from {args}:\n"
                f"Title: {metadata.get('title', 'N/A')}\n"
                f"Content length: {len(content)} characters\n\n"
                f"{content[:2000]}..."  # Return first 2000 chars to avoid huge responses
            )
        except Exception as e:
            return f"Error browsing URL: {e}"

    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))

    def _execute_search_web(self, args: str) -> str:
        """Perform a web search using multiple fallback methods.

        Args:
            args: The search query and optional number of results.

        Returns:
            The search results in JSON format.
        """
        # Parse the arguments (query and optional num_results)
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No search query specified."

        query = args_parts[0]
        num_results = 5  # Default

        if len(args_parts) > 1:
            try:
                num_results = int(args_parts[1])
            except ValueError:
                pass  # Use default if invalid number

        try:
            results = self.search_api.search(query, num_results=num_results)
            if results:
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            else:
                return f"No results found for query: {query}"
        except Exception as e:
            return f"Error performing web search: {e}"

    def _register_search_tools(self):
        """Register search-related tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="search_web",
            description="Perform a reliable web search using multiple fallback methods.",
            function=self._execute_search_web,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The search query."},
                    "num_results": {"type": "integer", "description": "Number of results to return. Defaults to 5.", "default": 5}
                },
                "required": ["query"]
            }
        ))

    def _register_scrape_tools(self):
        """Register web scraping tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="scrape_web",
            description="Scrape content from a website with advanced fallback mechanisms.",
            function=self._execute_scrape_web,
            parameters={
                "type": "object",
                "properties": {
                    "url": {"type": "string", "description": "The URL to scrape."}
                },
                "required": ["url"]
            }
        ))

    def _execute_scrape_web(self, args: str) -> str:
        """Scrape content from a website using multiple fallback methods.

        Args:
            args: The URL to scrape.

        Returns:
            The scraped content in a structured format.
        """
        if not args.strip():
            return "Error: No URL specified for scraping."

        try:
            # Scrape the URL with fallback methods
            scraped_data = self.web_scraper.scrape(args)

            if scraped_data:
                return f"Scraped content from {args}:\n\n{json.dumps(scraped_data, indent=2)}"
            else:
                return f"No content could be scraped from {args}"
        except Exception as e:
            return f"Error scraping website: {e}"

    def _register_info_tools(self):
        """Register information processing tools with the tool manager."""
        self.tool_manager.register_tool(Tool(
            name="info_retrieve",
            description="Retrieve and synthesize information about a topic.",
            function=self._execute_info_retrieve,
            parameters={
                "type": "object",
                "properties": {
                    "topic": {"type": "string", "description": "The topic to retrieve information about."}
                },
                "required": ["topic"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_extract",
            description="Extract key information from a given text.",
            function=self._execute_info_extract,
            parameters={
                "type": "object",
                "properties": {
                    "text": {"type": "string", "description": "The text from which to extract information."}
                },
                "required": ["text"]
            }
        ))
        self.tool_manager.register_tool(Tool(
            name="info_format",
            description="Format information in a structured manner (e.g., markdown, JSON).",
            function=self._execute_info_format,
            parameters={
                "type": "object",
                "properties": {
                    "information": {"type": "string", "description": "The information to format."},
                    "format_type": {"type": "string", "description": "The desired format (e.g., 'markdown', 'json')."}
                },
                "required": ["information", "format_type"]
            }
        ))

    def _execute_info_retrieve(self, args: str) -> str:
        """Retrieve and synthesize information about a topic.

        Args:
            args: The topic to retrieve information about.

        Returns:
            The synthesized information about the topic.
        """
        if not args.strip():
            return "Error: No topic specified for information retrieval."

        try:
            result = self.info_synthesizer.retrieve(args)
            if result:
                return f"Information about '{args}':\n\n{result}"
            else:
                return f"No information found about topic: {args}"
        except Exception as e:
            return f"Error retrieving information: {e}"

    def _execute_info_extract(self, args: str) -> str:
        """Extract key information from a given text.

        Args:
            args: The text to extract information from.

        Returns:
            The extracted key information in a structured format.
        """
        if not args.strip():
            return "Error: No text provided for information extraction."

        try:
            extracted_info = self.info_synthesizer.extract(args)
            return f"Extracted information:\n\n{json.dumps(extracted_info, indent=2)}"
        except Exception as e:
            return f"Error extracting information: {e}"

    def _execute_info_format(self, args: str) -> str:
        """Format information in a specified structure.

        Args:
            args: The information and format type separated by a delimiter.

        Returns:
            The formatted information.
        """
        # Parse the arguments (information and format_type)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both information and format type must be specified."

        information = args_parts[0]
        format_type = args_parts[1].lower()

        try:
            formatted_info = self.info_synthesizer.format(information, format_type)
            return f"Formatted information ({format_type}):\n\n{formatted_info}"
        except Exception as e:
            return f"Error formatting information: {e}"

    def _register_ai_assistant_tool(self):
        self.tool_manager.register_tool(Tool(
            name="ai_assistant",
            description="Advanced AI code analysis, generation, and optimization.",
            function=self._execute_ai_assistant,
            parameters={
                "type": "object",
                "properties": {
                    "request_type": {"type": "string", "description": "Type of request (e.g., 'analyze', 'generate', 'optimize')."},
                    "content": {"type": "string", "description": "Content for the request (e.g., code, problem description)."},
                    "language": {"type": "string", "description": "Programming language if applicable."}
                },
                "required": ["request_type", "content"]
            }
        ))

    def _execute_ai_assistant(self, args: str) -> str:
        """Execute an AI assistant operation.

        Args:
            args: The request type and content separated by a delimiter.

        Returns:
            The result of the AI assistant operation.
        """
        # Parse the arguments (request_type and content)
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both request type and content must be specified."

        request_type = args_parts[0]
        content = args_parts[1]

        try:
            # Create an assistant request
            request = AssistantRequest(
                request_type=request_type,
                content=content,
                language="python"  # Default to python if not specified
            )

            # Process the request
            response = self.ai_code_assistant.process_request(request)

            if response.success:
                return f"AI Assistant response ({request_type}):\n\n{response.content}"
            else:
                return f"AI Assistant error: {response.error_message}"
        except Exception as e:
            return f"Error processing AI assistant request: {e}"

    def _execute_ai_assistant(self, args: str) -> str:
        """Execute an AI assistant operation.

        Args:
            args: The arguments for the AI assistant operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No AI assistant operation specified."

        request_type = args_parts[0]
        content = args_parts[1] if len(args_parts) > 1 else ""

        try:
            # Create an assistant request
            request = AssistantRequest(
                request_type=request_type,
                content=content,
                language="python"  # Default to python if not specified
            )

            # Process the request
            response = self.ai_code_assistant.process_request(request)

            if response.success:
                return f"AI Assistant response ({request_type}):\n\n{response.content}"
            else:
                return f"AI Assistant error: {response.error_message}"
        except Exception as e:
            return f"Error processing AI assistant request: {e}"

    def _register_rag_tool(self):
        self.tool_manager.register_tool(Tool(
            name="rag",
            description="Retrieval-augmented generation for answering questions based on a knowledge base.",
            function=self._execute_rag,
            parameters={
                "type": "object",
                "properties": {
                    "query": {"type": "string", "description": "The query for RAG."}
                },
                "required": ["query"]
            }
        ))

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation to answer questions based on knowledge base.

        Args:
            args: The query to process with RAG.

        Returns:
            The response from RAG system with relevant information.
        """
        if not args.strip():
            return "Error: No query specified for RAG operation."

        if not self._has_rag:
            return "Error: RAG functionality is not available. Required dependencies (faiss, sentence-transformers) not found."

        try:
            # Process the RAG query
            result = self.rag_tool.query(args)

            if result:
                return f"RAG response for '{args}':\n\n{result}"
            else:
                return f"No relevant information found for query: {args}"
        except Exception as e:
            return f"Error processing RAG query: {e}"

    def _execute_rag(self, args: str) -> str:
        """Execute a RAG operation.

        Args:
            args: The arguments for the RAG operation.

        Returns:
            The result of the operation.
        """
        if not args.strip():
            return "Error: No query specified for RAG operation."

        if not self._has_rag:
            return "Error: RAG functionality is not available. Required dependencies (faiss, sentence-transformers) not found."

        try:
            # Process the RAG query
            result = self.rag_tool.query(args)

            if result:
                return f"RAG response:\n\n{result}"
            else:
                return "No relevant information found in the knowledge base."
        except Exception as e:
            return f"Error processing RAG query: {e}"

    def _get_default_system_prompt(self) -> str:
        """Get the default system prompt.

        Returns:
            The default system prompt.
        """
        tools_list_str = ""
        for tool in self.tool_manager.get_all_tools():
            tools_list_str += f"- {tool.name}: {tool.description}\n"

        tool_examples = self.tool_manager.generate_tool_prompt_examples()

        return f"""
You are an advanced AI coding agent that can help with various tasks.
You have access to the following tools:
{tools_list_str}

When you need to use a tool, use the following JSON format:
```tool_code
{{
  "tool_name": "tool_name_here",
  "parameters": {{
    "param1": "value1",
    "param2": "value2"
  }}
}}
```

Here are examples of how to use each tool:
{tool_examples}

Always provide clear explanations of what you're doing and why.
If you're not sure about something, ask for clarification.
Be helpful, accurate, and concise.
"""

    def _prepare_conversation_history(self, conversation: Conversation) -> List[Dict[str, Any]]:
        """Prepare conversation history for the model.

        Args:
            conversation: The conversation to prepare history from.

        Returns:
            List of message dictionaries for the model.
        """
        # Convert conversation messages to the format expected by the model
        history = []

        # Add system prompt as the first message if not already in conversation
        if not any(msg.role == "system" for msg in conversation.messages):
            history.append({
                "role": "system",
                "content": self.system_prompt
            })

        # Add all messages from the conversation
        for message in conversation.messages:
            # Skip tool messages for now as they're handled separately
            if message.role not in ["tool", "tool_result"]:
                history.append({
                    "role": message.role,
                    "content": message.content
                })

        return history

    def process_message(self, message: str, conversation: Optional[Conversation] = None) -> str:
        """Process a message from the user.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.

        Returns:
            The response from the agent.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response = self.model_manager.generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        )

        # Process the response for tool calls
        processed_response = self._process_response(response, conversation)

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

        # Analyze the tool result and provide a status update
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)
        if tool_matches:
            tool_result = conversation.messages[-1].content
            match = next(tool_matches)
            tool_name = match.group(1)
            tool_args = match.group(2).strip()
            status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
            return f"{processed_response}\n\n{status_update}"

        return processed_response

    def stream_process_message(
        self,
        message: str,
        conversation: Optional[Conversation] = None,
        callback: Optional[Callable[[str], None]] = None
    ) -> Generator[str, None, None]:
        """Process a message from the user and stream the response.

        Args:
            message: The message from the user.
            conversation: The conversation to add the message to. If None, will use the current conversation.
            callback: A callback function to call with each chunk of the response.

        Yields:
            Chunks of the response.
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Add the user message to the conversation
        conversation.add_message("user", message)

        # Prepare conversation history
        conversation_history = self._prepare_conversation_history(conversation)

        # Generate the response using conversation history
        response_chunks = []
        for chunk in self.model_manager.stream_generate(
            prompt=message,
            system_prompt=self.system_prompt,
            conversation_history=conversation_history
        ):
            response_chunks.append(chunk)
            if callback:
                callback(chunk)
            yield chunk

        # Process the response for tool calls
        response = "".join(response_chunks)
        processed_response = self._process_response(response, conversation)

        # If the response was processed (tool calls), yield the processed response
        if processed_response != response:
            if callback:
                callback(processed_response)
            yield processed_response

        # Check for tool calls in the response
        tool_pattern = r"```(\w+)\n(.*?)```"
        tool_matches = re.finditer(tool_pattern, response, re.DOTALL)
        if tool_matches:
            try:
                tool_result = conversation.messages[-1].content
                match = next(tool_matches)
                tool_name = match.group(1)
                tool_args = match.group(2).strip()
                status_update = self._analyze_tool_result(tool_name, tool_args, tool_result)
                yield f"\n\n{status_update}"
            except StopIteration:
                pass

        # Add the assistant message to the conversation
        conversation.add_message("assistant", processed_response)

        # Check if we should summarize the conversation
        if (conversation.get_message_count() >= self.conversation_manager.auto_summarize_threshold and
            not conversation.summary):
            # Try to summarize the conversation
            self.conversation_manager.summarize_conversation(conversation, self.model_manager)

        # Save the conversation
        self.conversation_manager.save_conversation()

    def _process_response(self, response: str, conversation: Conversation) -> str:
        """Process a response for tool calls.

        Args:
            response: The response to process.
            conversation: The conversation to add tool messages to.

        Returns:
            The processed response.
        """
        # Check for tool calls - look for both code blocks and tool_code format
        tool_patterns = [
            r"```(\w+)\n(.*?)```",  # Standard code blocks
            r"```tool_code\s*\n(.*?)```",  # Tool code format
            r"<tool_call>\s*(\w+)\s*:\s*(.*?)</tool_call>"  # XML format
        ]

        processed_response = response

        for pattern in tool_patterns:
            tool_matches = list(re.finditer(pattern, response, re.DOTALL))

            for match in tool_matches:
                try:
                    if pattern == r"```tool_code\s*\n(.*?)```":
                        # Handle JSON tool format
                        tool_json = match.group(1).strip()
                        try:
                            import json
                            tool_data = json.loads(tool_json)
                            tool_name = tool_data.get('tool_name', '')
                            tool_args = tool_data.get('parameters', {})

                            # Convert parameters to string format
                            if isinstance(tool_args, dict):
                                if 'path' in tool_args and 'content' in tool_args:
                                    # File write operation
                                    tool_args_str = f"write {tool_args['path']} {tool_args['content']}"
                                elif 'path' in tool_args:
                                    # File read operation
                                    tool_args_str = f"read {tool_args['path']}"
                                else:
                                    tool_args_str = ' '.join(f"{k}={v}" for k, v in tool_args.items())
                            else:
                                tool_args_str = str(tool_args)
                        except json.JSONDecodeError:
                            continue
                    else:
                        # Handle standard format
                        tool_name = match.group(1)
                        tool_args_str = match.group(2).strip()

                    # Execute the tool if it exists
                    if tool_name in self.tools:
                        logger.info(f"Executing tool: {tool_name} with args: {tool_args_str[:100]}...")
                        tool_result = self.tools[tool_name](tool_args_str)

                        # Add the tool call and result to the conversation
                        conversation.add_message("tool", f"Tool: {tool_name}\nArgs: {tool_args_str}")
                        conversation.add_message("tool_result", tool_result)

                        # Create a formatted result
                        formatted_result = f"\nTool {tool_name} executed successfully. Result: {tool_result}"

                        # Replace the tool call with the result in the response
                        processed_response = processed_response.replace(match.group(0), formatted_result)

                        logger.info(f"Tool {tool_name} executed successfully")
                    else:
                        logger.warning(f"Tool {tool_name} not found in available tools")
                        error_msg = f"\nError: Tool '{tool_name}' not available. Available tools: {', '.join(self.tools.keys())}"
                        processed_response = processed_response.replace(match.group(0), error_msg)

                except Exception as e:
                    logger.error(f"Error processing tool call: {e}")
                    error_msg = f"\nError executing tool: {str(e)}"
                    processed_response = processed_response.replace(match.group(0), error_msg)

        return processed_response

    def _analyze_tool_result(self, tool_name: str, tool_args: str, tool_result: str) -> str:
        """Analyze the results of a tool call and provide a clear status update.

        Args:
            tool_name: The name of the tool that was called.
            tool_args: The arguments that were passed to the tool.
            tool_result: The result of the tool call.

        Returns:
            A clear status update for the user.
        """
        # Analyze the tool result and provide a clear status update
        if "Error" in tool_result:
            return f"Tool {tool_name} failed with the following error: {tool_result}"
        else:
            return f"Tool {tool_name} executed successfully. Result: {tool_result}"

    def _execute_shell(self, command: str) -> str:
        """Execute a shell command.

        Args:
            command: The command to execute.

        Returns:
            The result of the command.
        """
        try:
            stdout, stderr, return_code = self.shell_tool.execute(command)

            if return_code == 0:
                if stdout.strip():
                    return f"Command executed successfully:\n\n{stdout}"
                else:
                    return "Command executed successfully."
            else:
                return f"Command failed with return code {return_code}:\n\n{stderr}"

        except Exception as e:
            return f"Error executing command: {e}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read_file(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content
            # Use a more robust approach to split only on the first space
            # This ensures file content with spaces is handled correctly
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Write the file
                self.file_tool.write_file(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "append":
            # Append to a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse the file path and content using a more robust approach
            parts_text = args_parts[1]
            try:
                # Find the first space after the file path
                space_index = parts_text.index(' ')
                file_path = parts_text[:space_index].strip()
                content = parts_text[space_index+1:].strip()
            except ValueError:
                # No space found, treat the whole string as the file path
                return "Error: No file content specified."

            if not file_path:
                return "Error: No file path specified."

            if not content:
                return "Error: No file content specified."

            try:
                # Create parent directories if they don't exist
                path_obj = Path(self.workspace_dir) / file_path
                path_obj.parent.mkdir(parents=True, exist_ok=True)

                # Append to the file - read existing content first
                try:
                    existing_content = self.file_tool.read_file(file_path)
                    new_content = existing_content + "\n" + content
                except:
                    # File doesn't exist, just use the new content
                    new_content = content

                self.file_tool.write_file(file_path, new_content)
                return f"Content appended successfully to: {file_path}"
            except Exception as e:
                return f"Error appending to file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                file_path_obj = Path(self.workspace_dir) / file_path
                if file_path_obj.exists():
                    file_path_obj.unlink()
                    return f"File deleted successfully: {file_path}"
                else:
                    return f"File not found: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files in a directory
            directory = args_parts[1] if len(args_parts) > 1 else "."

            try:
                dir_path = Path(self.workspace_dir) / directory
                if not dir_path.exists():
                    return f"Directory not found: {directory}"

                files = []
                for item in dir_path.iterdir():
                    if item.is_file():
                        files.append(f"📄 {item.name}")
                    elif item.is_dir():
                        files.append(f"📁 {item.name}/")

                if files:
                    return f"Contents of {directory}:\n\n" + "\n".join(files)
                else:
                    return f"Directory {directory} is empty"
            except Exception as e:
                return f"Error listing files: {e}"

        elif operation == "search":
            # Search for files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            pattern = args_parts[1]

            try:
                import glob
                search_path = Path(self.workspace_dir) / "**" / pattern
                files = glob.glob(str(search_path), recursive=True)

                if files:
                    relative_files = [os.path.relpath(f, self.workspace_dir) for f in files]
                    return f"Files matching {pattern}:\n\n" + "\n".join(relative_files)
                else:
                    return f"No files found matching pattern: {pattern}"
            except Exception as e:
                return f"Error searching for files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}. Available operations: read, write, append, delete, list, search"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The arguments for the code execution.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split("\n", 1)
        if len(args_parts) < 2:
            return "Error: No code or language specified."

        language = args_parts[0].strip().lower()
        code = args_parts[1]

        # Check if the language is supported
        supported_languages = self.code_tool.get_supported_languages()
        if language not in supported_languages:
            return f"Error: Unsupported language '{language}'. Supported languages: {', '.join(supported_languages)}"

        # Log the execution attempt
        logger.info(f"Executing {language} code...")

        try:
            # Execute the code with proper error handling
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            # Format the response based on the execution result
            if return_code != 0:
                # Execution failed
                error_message = stderr.strip() if stderr.strip() else "Unknown error"
                logger.error(f"{language.capitalize()} execution failed with return code {return_code}: {error_message}")
                return f"{language.capitalize()} execution error (return code {return_code}):\n\n{stderr}"
            else:
                # Execution succeeded
                if stdout.strip():
                    logger.info(f"{language.capitalize()} execution succeeded with output")
                    return f"{language.capitalize()} execution output:\n\n{stdout}"
                else:
                    logger.info(f"{language.capitalize()} execution succeeded with no output")
                    return f"{language.capitalize()} code executed successfully with no output."

        except Exception as e:
            # Handle any exceptions during execution
            error_details = str(e)
            logger.error(f"Error executing {language} code: {error_details}")

            # Provide a more detailed error message
            if "timeout" in error_details.lower():
                return f"Error: {language.capitalize()} code execution timed out after 30 seconds. Please optimize your code or break it into smaller parts."
            elif "not found" in error_details.lower():
                return f"Error: Required interpreter or compiler for {language} not found. Please ensure {language} is installed on the system."
            else:
                return f"Error executing {language} code: {error_details}"

    def _execute_web(self, args: str) -> str:
        """Execute a web operation.

        Args:
            args: The arguments for the web operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No web operation specified."

        operation = args_parts[0]

        if operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                results = self.web_tool.search(query)
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching the web: {e}"

        elif operation == "fetch":
            # Fetch a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                content, metadata = self.web_tool.fetch_url(url)
                return f"Content from {url}:\n\nTitle: {metadata.get('title', 'N/A')}\n\n{content[:2000]}..."
            except Exception as e:
                return f"Error fetching URL: {e}"

        else:
            return f"Error: Unknown web operation: {operation}"

    def _execute_codebase(self, args: str) -> str:
        """Execute a codebase operation.

        Args:
            args: The arguments for the codebase operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No codebase operation specified."

        operation = args_parts[0]

        if operation == "find":
            # Find files
            pattern = args_parts[1] if len(args_parts) > 1 else "*"

            try:
                files = self.codebase_tool.find_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding files: {e}"

        elif operation == "find_code":
            # Find code files
            try:
                files = self.codebase_tool.find_code_files()
                return f"Code files found:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding code files: {e}"

        elif operation == "search":
            # Search for a pattern in code files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse the pattern and file pattern
            search_parts = args_parts[1].split(maxsplit=1)
            if len(search_parts) < 2:
                pattern = search_parts[0]
                file_pattern = "*"
            else:
                pattern = search_parts[0]
                file_pattern = search_parts[1]

            try:
                results = self.codebase_tool.search_code(pattern, file_pattern)
                return f"Search results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching code: {e}"

        elif operation == "analyze":
            # Analyze a code file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                results = self.codebase_tool.analyze_file(file_path)
                return f"Analysis results for {file_path}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error analyzing file: {e}"

        else:
            return f"Error: Unknown codebase operation: {operation}"

    def _execute_vision(self, args: str) -> str:
        """Execute a vision operation.

        Args:
            args: The arguments for the vision operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No vision operation specified."

        operation = args_parts[0]

        if operation == "take_screenshot":
            # Take a screenshot
            path = args_parts[1] if len(args_parts) > 1 else None

            try:
                screenshot_path = self.vision_tool.take_screenshot(path)
                return f"Screenshot taken and saved to: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "load_image":
            # Load an image
            if len(args_parts) < 2:
                return "Error: No image path specified."

            image_path = args_parts[1]

            try:
                image = self.vision_tool.load_image(image_path)
                return f"Image loaded: {image_path} (Size: {image.width}x{image.height})"
            except Exception as e:
                return f"Error loading image: {e}"

        else:
            return f"Error: Unknown vision operation: {operation}"


    def _execute_patch(self, args: str) -> str:
        """Execute a patch operation.

        Args:
            args: The arguments for the patch operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No patch operation specified."

        operation = args_parts[0]

        if operation == "apply":
            # Apply a patch
            if len(args_parts) < 2:
                return "Error: No patch arguments specified."

            # Parse the file path, original code, and updated code
            try:
                patch_parts = args_parts[1].split(maxsplit=2)
                if len(patch_parts) < 3:
                    return "Error: Insufficient patch arguments. Need file_path, original_code, and updated_code."

                file_path = patch_parts[0]
                original_code = patch_parts[1]
                updated_code = patch_parts[2]

                # Apply the patch
                result = self.patch_tool.apply_patch(file_path, original_code, updated_code)
                return f"Patch applied successfully to {file_path}"
            except Exception as e:
                return f"Error applying patch: {e}"

        else:
            return f"Error: Unknown patch operation: {operation}"

    def _execute_file(self, args: str) -> str:
        """Execute a file operation.

        Args:
            args: The arguments for the file operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No file operation specified."

        operation = args_parts[0]

        if operation == "read":
            # Read a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                content = self.file_tool.read(file_path)
                return f"File content:\n\n{content}"
            except Exception as e:
                return f"Error reading file: {e}"

        elif operation == "write":
            # Write a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            # Parse file path and content
            parts = args_parts[1].split(maxsplit=1)
            if len(parts) < 2:
                return "Error: No file content specified."

            file_path = parts[0]
            content = parts[1]

            try:
                self.file_tool.write(file_path, content)
                return f"File written successfully: {file_path}"
            except Exception as e:
                return f"Error writing file: {e}"

        elif operation == "delete":
            # Delete a file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                self.file_tool.delete(file_path)
                return f"File deleted successfully: {file_path}"
            except Exception as e:
                return f"Error deleting file: {e}"

        elif operation == "list":
            # List files
            path = args_parts[1] if len(args_parts) > 1 else "."

            try:
                files = self.file_tool.list(path)
                return f"Files in {path}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error listing files: {e}"

        else:
            return f"Error: Unknown file operation: {operation}"

    def _execute_web(self, args: str) -> str:
        """Execute a web operation.

        Args:
            args: The arguments for the web operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No web operation specified."

        operation = args_parts[0]

        if operation == "search":
            # Search the web
            if len(args_parts) < 2:
                return "Error: No search query specified."

            query = args_parts[1]

            try:
                results = self.web_tool.search(query)
                return f"Search results for '{query}':\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching the web: {e}"

        elif operation == "fetch":
            # Fetch a URL
            if len(args_parts) < 2:
                return "Error: No URL specified."

            url = args_parts[1]

            try:
                content, metadata = self.web_tool.fetch_url(url)
                return f"Content from {url}:\n\nTitle: {metadata.get('title', 'N/A')}\n\n{content[:2000]}..."
            except Exception as e:
                return f"Error fetching URL: {e}"

        else:
            return f"Error: Unknown web operation: {operation}"

    def _execute_codebase(self, args: str) -> str:
        """Execute a codebase operation.

        Args:
            args: The arguments for the codebase operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No codebase operation specified."

        operation = args_parts[0]

        if operation == "find":
            # Find files
            pattern = args_parts[1] if len(args_parts) > 1 else "*"

            try:
                files = self.codebase_tool.find_files(pattern)
                return f"Files matching {pattern}:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding files: {e}"

        elif operation == "find_code":
            # Find code files
            try:
                files = self.codebase_tool.find_code_files()
                return f"Code files found:\n\n{json.dumps(files, indent=2)}"
            except Exception as e:
                return f"Error finding code files: {e}"

        elif operation == "search":
            # Search for a pattern in code files
            if len(args_parts) < 2:
                return "Error: No search pattern specified."

            # Parse the pattern and file pattern
            search_parts = args_parts[1].split(maxsplit=1)
            if len(search_parts) < 2:
                pattern = search_parts[0]
                file_pattern = "*"
            else:
                pattern = search_parts[0]
                file_pattern = search_parts[1]

            try:
                results = self.codebase_tool.search(pattern, file_pattern)
                return f"Search results for {pattern} in {file_pattern}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error searching code: {e}"

        elif operation == "analyze":
            # Analyze a code file
            if len(args_parts) < 2:
                return "Error: No file path specified."

            file_path = args_parts[1]

            try:
                results = self.codebase_tool.analyze(file_path)
                return f"Analysis results for {file_path}:\n\n{json.dumps(results, indent=2)}"
            except Exception as e:
                return f"Error analyzing file: {e}"

        else:
            return f"Error: Unknown codebase operation: {operation}"

    def _execute_vision(self, args: str) -> str:
        """Execute a vision operation.

        Args:
            args: The arguments for the vision operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No vision operation specified."

        operation = args_parts[0]

        if operation == "take_screenshot":
            # Take a screenshot
            path = args_parts[1] if len(args_parts) > 1 else None

            try:
                screenshot_path = self.vision_tool.take_screenshot(path)
                return f"Screenshot taken and saved to: {screenshot_path}"
            except Exception as e:
                return f"Error taking screenshot: {e}"

        elif operation == "load_image":
            # Load an image
            if len(args_parts) < 2:
                return "Error: No image path specified."

            image_path = args_parts[1]

            try:
                image = self.vision_tool.load_image(image_path)
                return f"Image loaded: {image_path} (Size: {image.width}x{image.height})"
            except Exception as e:
                return f"Error loading image: {e}"

        else:
            return f"Error: Unknown vision operation: {operation}"

    def _execute_patch(self, args: str) -> str:
        """Execute a patch operation.

        Args:
            args: The arguments for the patch operation.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if not args_parts:
            return "Error: No patch operation specified."

        operation = args_parts[0]

        if operation == "apply":
            # Apply a patch
            if len(args_parts) < 2:
                return "Error: No patch arguments specified."

            # Parse the file path, original code, and updated code
            try:
                patch_parts = args_parts[1].split(maxsplit=2)
                if len(patch_parts) < 3:
                    return "Error: Insufficient patch arguments. Need file_path, original_code, and updated_code."

                file_path = patch_parts[0]
                original_code = patch_parts[1]
                updated_code = patch_parts[2]

                # Apply the patch
                success = self.patch_tool.apply(file_path, original_code, updated_code)
                if success:
                    return f"Patch applied successfully to {file_path}"
                else:
                    return f"Failed to apply patch to {file_path}"
            except Exception as e:
                return f"Error applying patch: {e}"

        else:
            return f"Error: Unknown patch operation: {operation}"

    def _execute_code(self, args: str) -> str:
        """Execute code in various programming languages.

        Args:
            args: The arguments for the code execution.

        Returns:
            The result of the operation.
        """
        # Parse the arguments
        args_parts = args.strip().split(maxsplit=1)
        if len(args_parts) < 2:
            return "Error: Both language and code must be specified."

        language = args_parts[0].lower()
        code = args_parts[1]

        try:
            # Execute the code
            stdout, stderr, return_code = self.code_tool.execute(code, language)

            if return_code == 0:
                if stdout:
                    return f"Code executed successfully:\n\n{stdout}"
                return "Code executed successfully (no output)"
            else:
                return f"Code execution failed (exit code {return_code}):\n\n{stderr}"
        except Exception as e:
            return f"Error executing code: {e}"

    def execute_iteratively(self, message: str, conversation: Optional[Conversation] = None) -> Generator[str, None, str]:
        """
        Execute one step at a time with thorough analysis and planning.

        This implements the user's requested workflow:
        1. Execute one step at a time
        2. Analyze results thoroughly after each step
        3. Plan next step based on analysis
        4. Continue iterative process until completion
        5. Validate against user requirements
        6. Provide clear status updates at each stage

        Args:
            message: The user's request
            conversation: The conversation context

        Yields:
            Status updates and results at each step

        Returns:
            Final result summary
        """
        # Get the conversation
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Step 1: Initial Analysis
        yield "🔍 **Step 1: Analyzing Request**"
        yield f"Request: {message}"

        # Analyze the request using the learning system
        analysis = self.learning_system.analyze_request(message)
        yield f"Analysis: {analysis.get('complexity', 'Standard')} complexity task"
        yield f"Estimated steps: {analysis.get('estimated_steps', 'Unknown')}"
        yield f"Required tools: {', '.join(analysis.get('required_tools', []))}"

        # Step 2: Planning
        yield "\n📋 **Step 2: Creating Execution Plan**"
        plan = self.adaptive_controller.create_execution_plan(message, analysis)
        for i, step in enumerate(plan.get('steps', []), 1):
            yield f"  {i}. {step['description']}"

        # Step 3: Iterative Execution
        results = []
        for step_num, step in enumerate(plan.get('steps', []), 1):
            yield f"\n⚡ **Step {step_num}: {step['description']}**"

            # Execute the step
            try:
                step_result = self._execute_step(step, conversation)
                results.append({
                    'step': step_num,
                    'description': step['description'],
                    'result': step_result,
                    'success': True
                })

                # Analyze the result
                yield f"✅ Step {step_num} completed successfully"
                yield f"Result: {step_result[:200]}..." if len(step_result) > 200 else f"Result: {step_result}"

                # Performance analysis
                performance = self.performance_analyzer.analyze_step_performance(step, step_result)
                if performance.get('warnings'):
                    yield f"⚠️  Performance warnings: {', '.join(performance['warnings'])}"

                # Error detection
                errors = self.error_detector.detect_errors(step_result)
                if errors:
                    yield f"🚨 Potential issues detected: {', '.join(errors)}"

            except Exception as e:
                error_msg = str(e)
                results.append({
                    'step': step_num,
                    'description': step['description'],
                    'result': error_msg,
                    'success': False
                })
                yield f"❌ Step {step_num} failed: {error_msg}"

                # Try to recover or suggest alternatives
                recovery = self.adaptive_controller.suggest_recovery(step, error_msg)
                if recovery:
                    yield f"💡 Recovery suggestion: {recovery}"

            # Plan next step based on current results
            if step_num < len(plan.get('steps', [])):
                next_step_analysis = self.adaptive_controller.analyze_next_step(results, plan['steps'][step_num])
                if next_step_analysis.get('modifications'):
                    yield f"🔄 Adjusting next step based on current results"

        # Step 4: Final Validation
        yield "\n✅ **Step 4: Validation & Summary**"

        # Validate against original requirements
        validation = self.learning_system.validate_results(message, results)
        yield f"Requirements validation: {'✅ Passed' if validation.get('passed') else '❌ Failed'}"

        if not validation.get('passed'):
            yield f"Missing requirements: {', '.join(validation.get('missing', []))}"

        # Generate final summary
        summary = self._generate_execution_summary(message, results, validation)
        yield f"\n📊 **Execution Summary:**"
        yield f"Total steps: {len(results)}"
        yield f"Successful steps: {sum(1 for r in results if r['success'])}"
        yield f"Failed steps: {sum(1 for r in results if not r['success'])}"
        yield f"Overall success: {'✅ Yes' if validation.get('passed') else '❌ No'}"

        # Learn from this execution
        self.learning_system.learn_from_execution(message, results, validation)

        return summary

    def _execute_step(self, step: Dict[str, Any], conversation: Conversation) -> str:
        """Execute a single step in the iterative process."""
        tool_name = step.get('tool')
        parameters = step.get('parameters', {})

        if tool_name and tool_name in self.tools:
            # Execute using the specified tool
            if isinstance(parameters, dict):
                # Convert parameters to string format expected by tools
                param_str = ' '.join(f"{k}={v}" for k, v in parameters.items())
            else:
                param_str = str(parameters)

            return self.tools[tool_name](param_str)
        else:
            # Execute as a general AI request
            return self.process_message(step.get('description', ''), conversation)

    def _generate_execution_summary(self, original_request: str, results: List[Dict], validation: Dict) -> str:
        """Generate a comprehensive summary of the execution."""
        summary = f"""
Execution Summary for: "{original_request}"

Steps Executed: {len(results)}
Success Rate: {sum(1 for r in results if r['success'])}/{len(results)} ({(sum(1 for r in results if r['success'])/len(results)*100):.1f}%)

Detailed Results:
"""
        for result in results:
            status = "✅" if result['success'] else "❌"
            summary += f"{status} Step {result['step']}: {result['description']}\n"
            if not result['success']:
                summary += f"   Error: {result['result']}\n"

        summary += f"\nValidation: {'✅ Passed' if validation.get('passed') else '❌ Failed'}"

        if validation.get('recommendations'):
            summary += f"\nRecommendations:\n"
            for rec in validation['recommendations']:
                summary += f"• {rec}\n"

        return summary

    def execute_parallel_tasks(self, tasks: List[Dict[str, Any]], conversation: Optional[Conversation] = None) -> Dict[str, Any]:
        """
        Execute multiple tasks in parallel using multi-threading.

        Args:
            tasks: List of tasks to execute in parallel
            conversation: The conversation context

        Returns:
            Dictionary containing results from all parallel tasks
        """
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        logger.info(f"Starting parallel execution of {len(tasks)} tasks")

        # Create executor for parallel tasks
        executor_name = f"parallel_tasks_{int(time.time())}"
        self.thread_manager.create_executor(executor_name, workers=min(len(tasks), 4))

        # Submit all tasks
        task_futures = {}
        for i, task in enumerate(tasks):
            task_id = f"task_{i}"
            future = self.thread_manager.submit_task(
                executor_name,
                task_id,
                self._execute_single_task,
                task,
                conversation
            )
            task_futures[task_id] = future

        # Collect results
        results = {}
        completed_tasks = 0

        for task_id, future in task_futures.items():
            try:
                result = self.thread_manager.get_result(executor_name, task_id, timeout=30)
                results[task_id] = {
                    'success': True,
                    'result': result,
                    'task': tasks[int(task_id.split('_')[1])]
                }
                completed_tasks += 1
                logger.info(f"Task {task_id} completed successfully")
            except Exception as e:
                results[task_id] = {
                    'success': False,
                    'error': str(e),
                    'task': tasks[int(task_id.split('_')[1])]
                }
                logger.error(f"Task {task_id} failed: {e}")

        # Clean up executor
        self.thread_manager.shutdown_executor(executor_name)

        return {
            'total_tasks': len(tasks),
            'completed_tasks': completed_tasks,
            'success_rate': completed_tasks / len(tasks) if tasks else 0,
            'results': results
        }

    def _execute_single_task(self, task: Dict[str, Any], conversation: Conversation) -> str:
        """Execute a single task (used for parallel execution)."""
        return self._execute_step(task, conversation)

    def execute_with_context_awareness(self, message: str, conversation: Optional[Conversation] = None) -> str:
        """
        Execute a request with context-aware processing and predictive prefetching.

        Args:
            message: The user's request
            conversation: The conversation context

        Returns:
            The response with context-aware enhancements
        """
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Analyze context
        context_analysis = self.context_analyzer.analyze_context(message, conversation)

        # Predictive prefetching
        prefetch_suggestions = self.prefetcher.predict_next_actions(message, context_analysis)

        # Start prefetching in background
        if prefetch_suggestions:
            self._start_background_prefetch(prefetch_suggestions)

        # Code prediction if applicable
        code_predictions = None
        if context_analysis.get('involves_code', False):
            code_predictions = self.code_predictor.predict_code_completion(message, context_analysis)

        # Execute the main request
        response = self.process_message(message, conversation)

        # Enhance response with context-aware information
        if context_analysis.get('complexity_score', 0) > 0.7:
            response += f"\n\n🧠 **Context Analysis**: This appears to be a {context_analysis.get('complexity_level', 'standard')} complexity task."

        if prefetch_suggestions:
            response += f"\n\n🔮 **Predictive Insights**: Based on your request, you might also want to: {', '.join(prefetch_suggestions[:3])}"

        if code_predictions:
            response += f"\n\n💡 **Code Suggestions**: {code_predictions.get('suggestion', '')}"

        return response

    def _start_background_prefetch(self, suggestions: List[str]) -> None:
        """Start background prefetching of suggested resources."""
        executor_name = "prefetch_executor"

        if executor_name not in self.thread_manager.executors:
            self.thread_manager.create_executor(executor_name, workers=2)

        for i, suggestion in enumerate(suggestions[:3]):  # Limit to 3 prefetch operations
            task_id = f"prefetch_{i}"
            self.thread_manager.submit_task(
                executor_name,
                task_id,
                self._prefetch_resource,
                suggestion
            )

    def _prefetch_resource(self, resource_hint: str) -> str:
        """Prefetch a resource based on a hint."""
        try:
            # Simple prefetching logic - can be enhanced
            if 'file' in resource_hint.lower():
                # Prefetch file operations
                return "File operations prefetched"
            elif 'web' in resource_hint.lower():
                # Prefetch web resources
                return "Web resources prefetched"
            elif 'code' in resource_hint.lower():
                # Prefetch code analysis
                return "Code analysis prefetched"
            else:
                return f"Generic prefetch for: {resource_hint}"
        except Exception as e:
            logger.warning(f"Prefetch failed for {resource_hint}: {e}")
            return f"Prefetch failed: {e}"

    def execute_with_error_recovery(self, message: str, conversation: Optional[Conversation] = None, max_retries: int = 3) -> str:
        """
        Execute a request with comprehensive error handling and recovery.

        Args:
            message: The user's request
            conversation: The conversation context
            max_retries: Maximum number of retry attempts

        Returns:
            The response with error recovery if needed
        """
        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        last_error = None

        for attempt in range(max_retries + 1):
            try:
                # Log the attempt
                if attempt > 0:
                    logger.info(f"Retry attempt {attempt} for request: {message[:50]}...")

                # Execute the request
                if hasattr(self, 'iterative_mode') and self.iterative_mode:
                    # Use iterative execution with error handling
                    result_parts = []
                    for part in self.execute_iteratively(message, conversation):
                        result_parts.append(part)
                    return '\n'.join(result_parts)
                else:
                    # Use standard execution
                    return self.process_message(message, conversation)

            except Exception as e:
                last_error = e
                error_msg = str(e)
                logger.error(f"Execution attempt {attempt + 1} failed: {error_msg}")

                # Analyze the error
                error_analysis = self.error_detector.analyze_error(error_msg)

                # Try recovery strategies
                if attempt < max_retries:
                    recovery_strategy = self._determine_recovery_strategy(error_msg, attempt)
                    logger.info(f"Attempting recovery strategy: {recovery_strategy}")

                    # Apply recovery strategy
                    if recovery_strategy == "simplify_request":
                        message = self._simplify_request(message)
                    elif recovery_strategy == "break_down_request":
                        return self._break_down_and_execute(message, conversation)
                    elif recovery_strategy == "use_fallback_tools":
                        return self._execute_with_fallback_tools(message, conversation)

                    # Wait before retry
                    time.sleep(min(2 ** attempt, 10))  # Exponential backoff
                else:
                    # Final attempt failed, provide comprehensive error info
                    error_report = self._generate_error_report(message, last_error, attempt + 1)
                    return f"❌ **Execution Failed After {attempt + 1} Attempts**\n\n{error_report}"

        # This should never be reached, but just in case
        return f"❌ **Unexpected Error**: Failed to execute request after {max_retries + 1} attempts"

    def _determine_recovery_strategy(self, error_msg: str, attempt: int) -> str:
        """Determine the best recovery strategy based on the error."""
        error_lower = error_msg.lower()

        if attempt == 0:
            # First retry: try simplification
            if any(word in error_lower for word in ['complex', 'timeout', 'memory']):
                return "simplify_request"
            elif any(word in error_lower for word in ['not found', 'missing', 'invalid']):
                return "use_fallback_tools"
            else:
                return "break_down_request"
        elif attempt == 1:
            # Second retry: try breaking down
            return "break_down_request"
        else:
            # Final retry: use fallback tools
            return "use_fallback_tools"

    def _simplify_request(self, message: str) -> str:
        """Simplify a complex request."""
        # Remove complex modifiers
        simplified = message.replace("comprehensive", "").replace("detailed", "").replace("advanced", "")
        simplified = simplified.replace("optimize", "improve").replace("analyze", "check")
        return simplified.strip()

    def _break_down_and_execute(self, message: str, conversation: Conversation) -> str:
        """Break down a complex request into simpler parts."""
        try:
            # Simple breakdown logic
            parts = []
            message_lower = message.lower()

            if 'and' in message_lower:
                parts = [part.strip() for part in message.split(' and ')]
            elif ',' in message:
                parts = [part.strip() for part in message.split(',')]
            else:
                # Break by sentences
                parts = [part.strip() for part in message.split('.') if part.strip()]

            if len(parts) <= 1:
                parts = [message]  # Can't break down further

            results = []
            for i, part in enumerate(parts[:3]):  # Limit to 3 parts
                try:
                    result = self.process_message(part, conversation)
                    results.append(f"**Part {i+1}**: {result}")
                except Exception as e:
                    results.append(f"**Part {i+1} Failed**: {str(e)}")

            return "\n\n".join(results)

        except Exception as e:
            return f"Failed to break down request: {str(e)}"

    def _execute_with_fallback_tools(self, message: str, conversation: Conversation) -> str:
        """Execute using fallback tools when primary execution fails."""
        try:
            # Try basic tools first
            message_lower = message.lower()

            if any(word in message_lower for word in ['file', 'read', 'write']):
                return self._execute_file_operation(f"list .")
            elif any(word in message_lower for word in ['code', 'execute']):
                return "Code execution is currently unavailable. Please try a simpler approach."
            elif any(word in message_lower for word in ['search', 'find']):
                return self._execute_search_operation(message_lower)
            else:
                return f"I understand you want to: {message}\n\nHowever, I'm experiencing technical difficulties. Please try rephrasing your request or breaking it into smaller parts."

        except Exception as e:
            return f"Fallback execution also failed: {str(e)}"

    def _generate_error_report(self, original_request: str, error: Exception, attempts: int) -> str:
        """Generate a comprehensive error report."""
        error_type = type(error).__name__
        error_msg = str(error)

        report = f"""
**Original Request**: {original_request}

**Error Type**: {error_type}
**Error Message**: {error_msg}
**Attempts Made**: {attempts}

**Possible Causes**:
"""

        # Analyze possible causes
        if "timeout" in error_msg.lower():
            report += "• Request took too long to process\n• System may be overloaded\n"
        elif "permission" in error_msg.lower():
            report += "• Insufficient permissions to access resource\n• File or directory access denied\n"
        elif "not found" in error_msg.lower():
            report += "• Required file, command, or resource not found\n• Path may be incorrect\n"
        elif "memory" in error_msg.lower():
            report += "• Insufficient memory to complete operation\n• Request may be too large\n"
        else:
            report += "• Unexpected system error\n• Request may be malformed\n"

        report += """
**Suggestions**:
• Try breaking your request into smaller parts
• Check if all required files and resources exist
• Ensure you have necessary permissions
• Try rephrasing your request more simply
• Contact support if the problem persists
"""

        return report

    def execute_with_performance_optimization(self, message: str, conversation: Optional[Conversation] = None) -> Tuple[str, Dict[str, Any]]:
        """
        Execute a request with performance optimization and monitoring.

        Args:
            message: The user's request
            conversation: The conversation context

        Returns:
            Tuple of (response, performance_metrics)
        """
        start_time = time.time()

        if conversation is None:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            conversation = self.conversation_manager.current_conversation

        # Check cache first
        cache_key = self._generate_cache_key(message, conversation)
        cached_response = self.cache_system.get(cache_key)

        if cached_response:
            logger.info(f"Cache hit for request: {message[:50]}...")
            return cached_response['response'], {
                'execution_time': time.time() - start_time,
                'cache_hit': True,
                'cached_at': cached_response['timestamp']
            }

        # Performance monitoring
        memory_before = self._get_memory_usage()

        # Execute with optimization
        try:
            # Analyze request for optimization opportunities
            optimization_analysis = self.performance_analyzer.analyze_request_complexity(message)

            # Choose execution strategy based on complexity
            if optimization_analysis.get('complexity_score', 0.5) > 0.8:
                # High complexity - use parallel execution if possible
                response = self._execute_with_parallel_optimization(message, conversation)
            elif optimization_analysis.get('involves_multiple_operations', False):
                # Multiple operations - use pipeline optimization
                response = self._execute_with_pipeline_optimization(message, conversation)
            else:
                # Standard execution with monitoring
                response = self.process_message(message, conversation)

            # Cache the response
            self.cache_system.set(cache_key, {
                'response': response,
                'timestamp': time.time(),
                'message': message
            })

            # Calculate performance metrics
            execution_time = time.time() - start_time
            memory_after = self._get_memory_usage()

            performance_metrics = {
                'execution_time': execution_time,
                'memory_used': memory_after - memory_before,
                'cache_hit': False,
                'optimization_applied': optimization_analysis.get('optimization_strategy', 'standard'),
                'complexity_score': optimization_analysis.get('complexity_score', 0.5)
            }

            # Log performance if it's slow
            if execution_time > 10:
                logger.warning(f"Slow execution detected: {execution_time:.2f}s for request: {message[:50]}...")

            return response, performance_metrics

        except Exception as e:
            logger.error(f"Performance-optimized execution failed: {e}")
            # Fallback to error recovery
            response = self.execute_with_error_recovery(message, conversation)
            return response, {
                'execution_time': time.time() - start_time,
                'error': str(e),
                'fallback_used': True
            }

    def _generate_cache_key(self, message: str, conversation: Conversation) -> str:
        """Generate a cache key for the request."""
        import hashlib

        # Include message and recent conversation context
        context = ""
        if conversation and conversation.messages:
            # Include last 3 messages for context
            recent_messages = conversation.messages[-3:]
            context = " ".join([msg.content[:100] for msg in recent_messages])

        cache_input = f"{message}|{context}"
        return hashlib.md5(cache_input.encode()).hexdigest()

    def _get_memory_usage(self) -> float:
        """Get current memory usage in MB."""
        try:
            import psutil
            process = psutil.Process()
            return process.memory_info().rss / 1024 / 1024  # Convert to MB
        except ImportError:
            # Fallback if psutil not available
            import sys
            return sys.getsizeof(self) / 1024 / 1024

    def _execute_with_parallel_optimization(self, message: str, conversation: Conversation) -> str:
        """Execute with parallel optimization for complex requests."""
        try:
            # Break down complex request into parallel tasks
            tasks = self._analyze_and_break_down_request(message)

            if len(tasks) > 1:
                # Execute in parallel
                parallel_results = self.execute_parallel_tasks(tasks, conversation)

                # Combine results
                combined_response = "**Parallel Execution Results:**\n\n"
                for task_id, result in parallel_results['results'].items():
                    if result['success']:
                        combined_response += f"• {result['task']['description']}: {result['result'][:200]}...\n"
                    else:
                        combined_response += f"• {result['task']['description']}: Failed - {result['error']}\n"

                combined_response += f"\n**Summary**: {parallel_results['completed_tasks']}/{parallel_results['total_tasks']} tasks completed successfully."
                return combined_response
            else:
                # Single task, use standard execution
                return self.process_message(message, conversation)

        except Exception as e:
            logger.error(f"Parallel optimization failed: {e}")
            return self.process_message(message, conversation)

    def _execute_with_pipeline_optimization(self, message: str, conversation: Conversation) -> str:
        """Execute with pipeline optimization for multi-step requests."""
        try:
            # Identify pipeline stages
            stages = self._identify_pipeline_stages(message)

            if len(stages) > 1:
                results = []
                intermediate_data = {}

                for i, stage in enumerate(stages):
                    # Execute stage with data from previous stages
                    stage_input = self._prepare_stage_input(stage, intermediate_data)
                    stage_result = self._execute_step(stage_input, conversation)

                    results.append(f"**Stage {i+1}**: {stage_result}")
                    intermediate_data[f"stage_{i}"] = stage_result

                return "\n\n".join(results)
            else:
                return self.process_message(message, conversation)

        except Exception as e:
            logger.error(f"Pipeline optimization failed: {e}")
            return self.process_message(message, conversation)

    def _analyze_and_break_down_request(self, message: str) -> List[Dict[str, Any]]:
        """Analyze and break down a complex request into parallel tasks."""
        tasks = []
        message_lower = message.lower()

        # Simple heuristics for task breakdown
        if 'and' in message_lower:
            parts = message.split(' and ')
            for i, part in enumerate(parts):
                tasks.append({
                    'description': part.strip(),
                    'tool': self._determine_primary_tool(part),
                    'parameters': {'operation': part.strip()}
                })
        elif any(word in message_lower for word in ['analyze', 'check', 'review']):
            # Analysis tasks can often be parallelized
            if 'file' in message_lower:
                tasks.append({'description': 'Analyze file structure', 'tool': 'codebase'})
                tasks.append({'description': 'Check file content', 'tool': 'file'})
            if 'code' in message_lower:
                tasks.append({'description': 'Analyze code quality', 'tool': 'code'})
                tasks.append({'description': 'Check dependencies', 'tool': 'codebase'})
        else:
            # Single task
            tasks.append({
                'description': message,
                'tool': self._determine_primary_tool(message),
                'parameters': {'operation': message}
            })

        return tasks

    def _identify_pipeline_stages(self, message: str) -> List[Dict[str, Any]]:
        """Identify pipeline stages in a multi-step request."""
        stages = []
        message_lower = message.lower()

        # Look for sequential indicators
        if 'then' in message_lower:
            parts = message.split(' then ')
            for part in parts:
                stages.append({'description': part.strip(), 'type': 'sequential'})
        elif 'after' in message_lower:
            parts = message.split(' after ')
            for part in parts:
                stages.append({'description': part.strip(), 'type': 'sequential'})
        else:
            # Single stage
            stages.append({'description': message, 'type': 'single'})

        return stages

    def _prepare_stage_input(self, stage: Dict[str, Any], intermediate_data: Dict[str, Any]) -> Dict[str, Any]:
        """Prepare input for a pipeline stage using intermediate data."""
        stage_input = stage.copy()

        # Add context from previous stages
        if intermediate_data:
            stage_input['context'] = intermediate_data
            stage_input['description'] = f"{stage['description']} (with context from previous stages)"

        return stage_input

    def _determine_primary_tool(self, text: str) -> str:
        """Determine the primary tool needed for a text."""
        text_lower = text.lower()

        if any(word in text_lower for word in ['file', 'read', 'write']):
            return 'file'
        elif any(word in text_lower for word in ['code', 'execute', 'run']):
            return 'code'
        elif any(word in text_lower for word in ['web', 'url', 'http']):
            return 'web'
        elif any(word in text_lower for word in ['search', 'find']):
            return 'search'
        elif any(word in text_lower for word in ['codebase', 'project']):
            return 'codebase'
        else:
            return 'general'

    def analyze_project_structure(self, project_path: str = None) -> Dict[str, Any]:
        """
        Analyze the structure of a full-stack project.

        Args:
            project_path: Path to the project (defaults to workspace_dir)

        Returns:
            Comprehensive project analysis
        """
        if project_path is None:
            project_path = self.workspace_dir

        analysis = {
            'project_type': 'unknown',
            'languages': [],
            'frameworks': [],
            'dependencies': {},
            'structure': {},
            'build_tools': [],
            'test_frameworks': [],
            'deployment_configs': [],
            'documentation': [],
            'issues': [],
            'recommendations': []
        }

        try:
            # Analyze project structure
            project_files = self._scan_project_files(project_path)
            analysis['structure'] = project_files

            # Detect project type and languages
            analysis['project_type'], analysis['languages'] = self._detect_project_type(project_files)

            # Detect frameworks
            analysis['frameworks'] = self._detect_frameworks(project_files)

            # Analyze dependencies
            analysis['dependencies'] = self._analyze_dependencies(project_path, project_files)

            # Detect build tools
            analysis['build_tools'] = self._detect_build_tools(project_files)

            # Detect test frameworks
            analysis['test_frameworks'] = self._detect_test_frameworks(project_files)

            # Check deployment configurations
            analysis['deployment_configs'] = self._detect_deployment_configs(project_files)

            # Find documentation
            analysis['documentation'] = self._find_documentation(project_files)

            # Identify issues and provide recommendations
            analysis['issues'], analysis['recommendations'] = self._analyze_project_health(analysis)

            logger.info(f"Project analysis completed for: {project_path}")
            return analysis

        except Exception as e:
            logger.error(f"Project analysis failed: {e}")
            analysis['error'] = str(e)
            return analysis

    def _scan_project_files(self, project_path: str) -> Dict[str, Any]:
        """Scan and categorize project files."""
        structure = {
            'total_files': 0,
            'directories': [],
            'source_files': [],
            'config_files': [],
            'documentation_files': [],
            'test_files': [],
            'build_files': [],
            'other_files': []
        }

        try:
            for root, dirs, files in os.walk(project_path):
                # Skip hidden directories and common ignore patterns
                dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['node_modules', '__pycache__', 'venv', 'env']]

                rel_root = os.path.relpath(root, project_path)
                if rel_root != '.':
                    structure['directories'].append(rel_root)

                for file in files:
                    if file.startswith('.'):
                        continue

                    file_path = os.path.join(rel_root, file) if rel_root != '.' else file
                    structure['total_files'] += 1

                    # Categorize files
                    if self._is_source_file(file):
                        structure['source_files'].append(file_path)
                    elif self._is_config_file(file):
                        structure['config_files'].append(file_path)
                    elif self._is_documentation_file(file):
                        structure['documentation_files'].append(file_path)
                    elif self._is_test_file(file):
                        structure['test_files'].append(file_path)
                    elif self._is_build_file(file):
                        structure['build_files'].append(file_path)
                    else:
                        structure['other_files'].append(file_path)

            return structure

        except Exception as e:
            logger.error(f"Failed to scan project files: {e}")
            return structure

    def _is_source_file(self, filename: str) -> bool:
        """Check if file is a source code file."""
        source_extensions = [
            '.py', '.js', '.ts', '.jsx', '.tsx', '.java', '.cpp', '.c', '.h',
            '.cs', '.php', '.rb', '.go', '.rs', '.swift', '.kt', '.scala',
            '.html', '.css', '.scss', '.sass', '.less', '.vue', '.svelte'
        ]
        return any(filename.lower().endswith(ext) for ext in source_extensions)

    def _is_config_file(self, filename: str) -> bool:
        """Check if file is a configuration file."""
        config_files = [
            'package.json', 'requirements.txt', 'Pipfile', 'poetry.lock',
            'Cargo.toml', 'go.mod', 'pom.xml', 'build.gradle', 'composer.json',
            '.env', 'config.json', 'settings.json', 'tsconfig.json',
            'webpack.config.js', 'vite.config.js', 'next.config.js'
        ]
        config_extensions = ['.json', '.yaml', '.yml', '.toml', '.ini', '.conf']

        return (filename.lower() in [f.lower() for f in config_files] or
                any(filename.lower().endswith(ext) for ext in config_extensions))

    def _is_documentation_file(self, filename: str) -> bool:
        """Check if file is documentation."""
        doc_files = ['README.md', 'README.txt', 'CHANGELOG.md', 'LICENSE', 'CONTRIBUTING.md']
        doc_extensions = ['.md', '.rst', '.txt', '.doc', '.docx']

        return (filename in doc_files or
                any(filename.lower().endswith(ext) for ext in doc_extensions))

    def _is_test_file(self, filename: str) -> bool:
        """Check if file is a test file."""
        return ('test' in filename.lower() or
                filename.lower().startswith('test_') or
                filename.lower().endswith('_test.py') or
                filename.lower().endswith('.test.js') or
                filename.lower().endswith('.spec.js'))

    def _is_build_file(self, filename: str) -> bool:
        """Check if file is a build file."""
        build_files = [
            'Makefile', 'Dockerfile', 'docker-compose.yml', 'build.sh',
            'deploy.sh', '.github', '.gitlab-ci.yml', 'Jenkinsfile'
        ]
        return filename in build_files or 'build' in filename.lower()

    def _detect_project_type(self, project_files: Dict[str, Any]) -> Tuple[str, List[str]]:
        """Detect project type and programming languages."""
        languages = set()
        project_type = 'unknown'

        # Analyze source files for languages
        for file_path in project_files.get('source_files', []):
            ext = os.path.splitext(file_path)[1].lower()
            lang_map = {
                '.py': 'Python', '.js': 'JavaScript', '.ts': 'TypeScript',
                '.jsx': 'React', '.tsx': 'React TypeScript', '.java': 'Java',
                '.cpp': 'C++', '.c': 'C', '.cs': 'C#', '.php': 'PHP',
                '.rb': 'Ruby', '.go': 'Go', '.rs': 'Rust', '.swift': 'Swift',
                '.kt': 'Kotlin', '.scala': 'Scala', '.html': 'HTML',
                '.css': 'CSS', '.scss': 'SCSS', '.vue': 'Vue.js'
            }
            if ext in lang_map:
                languages.add(lang_map[ext])

        # Detect project type based on config files
        config_files = [f.lower() for f in project_files.get('config_files', [])]

        if 'package.json' in config_files:
            if any('react' in f or 'next' in f for f in config_files):
                project_type = 'React/Next.js Web Application'
            elif any('vue' in f for f in config_files):
                project_type = 'Vue.js Web Application'
            else:
                project_type = 'Node.js Application'
        elif any(f in config_files for f in ['requirements.txt', 'pipfile', 'poetry.lock']):
            if any('django' in f or 'flask' in f for f in project_files.get('source_files', [])):
                project_type = 'Python Web Application'
            else:
                project_type = 'Python Application'
        elif 'pom.xml' in config_files or 'build.gradle' in config_files:
            project_type = 'Java Application'
        elif 'cargo.toml' in config_files:
            project_type = 'Rust Application'
        elif 'go.mod' in config_files:
            project_type = 'Go Application'

        return project_type, list(languages)

    def _detect_frameworks(self, project_files: Dict[str, Any]) -> List[str]:
        """Detect frameworks used in the project."""
        frameworks = set()

        # Check source files for framework indicators
        source_files = project_files.get('source_files', [])
        for file_path in source_files:
            file_lower = file_path.lower()

            # Web frameworks
            if 'react' in file_lower or file_path.endswith('.jsx') or file_path.endswith('.tsx'):
                frameworks.add('React')
            if 'vue' in file_lower or file_path.endswith('.vue'):
                frameworks.add('Vue.js')
            if 'angular' in file_lower:
                frameworks.add('Angular')
            if 'django' in file_lower:
                frameworks.add('Django')
            if 'flask' in file_lower:
                frameworks.add('Flask')
            if 'express' in file_lower:
                frameworks.add('Express.js')
            if 'spring' in file_lower:
                frameworks.add('Spring')

        # Check config files
        config_files = project_files.get('config_files', [])
        for config_file in config_files:
            config_lower = config_file.lower()
            if 'next.config' in config_lower:
                frameworks.add('Next.js')
            if 'nuxt.config' in config_lower:
                frameworks.add('Nuxt.js')
            if 'webpack.config' in config_lower:
                frameworks.add('Webpack')
            if 'vite.config' in config_lower:
                frameworks.add('Vite')

        return list(frameworks)

    def _analyze_dependencies(self, project_path: str, project_files: Dict[str, Any]) -> Dict[str, Any]:
        """Analyze project dependencies."""
        dependencies = {
            'package_managers': [],
            'dependencies': {},
            'dev_dependencies': {},
            'outdated': [],
            'security_issues': []
        }

        config_files = project_files.get('config_files', [])

        # Analyze package.json (Node.js)
        if 'package.json' in [f.lower() for f in config_files]:
            dependencies['package_managers'].append('npm/yarn')
            try:
                package_json_path = os.path.join(project_path, 'package.json')
                if os.path.exists(package_json_path):
                    with open(package_json_path, 'r') as f:
                        package_data = json.load(f)
                        dependencies['dependencies'].update(package_data.get('dependencies', {}))
                        dependencies['dev_dependencies'].update(package_data.get('devDependencies', {}))
            except Exception as e:
                logger.warning(f"Failed to parse package.json: {e}")

        # Analyze requirements.txt (Python)
        if 'requirements.txt' in [f.lower() for f in config_files]:
            dependencies['package_managers'].append('pip')
            try:
                req_path = os.path.join(project_path, 'requirements.txt')
                if os.path.exists(req_path):
                    with open(req_path, 'r') as f:
                        for line in f:
                            line = line.strip()
                            if line and not line.startswith('#'):
                                if '==' in line:
                                    pkg, version = line.split('==', 1)
                                    dependencies['dependencies'][pkg.strip()] = version.strip()
                                else:
                                    dependencies['dependencies'][line] = 'latest'
            except Exception as e:
                logger.warning(f"Failed to parse requirements.txt: {e}")

        # Analyze Cargo.toml (Rust)
        if 'cargo.toml' in [f.lower() for f in config_files]:
            dependencies['package_managers'].append('cargo')
            # Could add TOML parsing here

        return dependencies

    def _detect_build_tools(self, project_files: Dict[str, Any]) -> List[str]:
        """Detect build tools used in the project."""
        build_tools = set()

        config_files = [f.lower() for f in project_files.get('config_files', [])]
        build_files = [f.lower() for f in project_files.get('build_files', [])]

        # Common build tools
        if 'webpack.config.js' in config_files:
            build_tools.add('Webpack')
        if 'vite.config.js' in config_files:
            build_tools.add('Vite')
        if 'rollup.config.js' in config_files:
            build_tools.add('Rollup')
        if 'gulpfile.js' in config_files:
            build_tools.add('Gulp')
        if 'gruntfile.js' in config_files:
            build_tools.add('Grunt')
        if 'makefile' in build_files:
            build_tools.add('Make')
        if 'dockerfile' in build_files:
            build_tools.add('Docker')
        if 'docker-compose.yml' in build_files:
            build_tools.add('Docker Compose')

        return list(build_tools)

    def _detect_test_frameworks(self, project_files: Dict[str, Any]) -> List[str]:
        """Detect testing frameworks used in the project."""
        test_frameworks = set()

        test_files = project_files.get('test_files', [])
        config_files = project_files.get('config_files', [])

        # Analyze test files
        for test_file in test_files:
            test_lower = test_file.lower()
            if 'jest' in test_lower:
                test_frameworks.add('Jest')
            if 'mocha' in test_lower:
                test_frameworks.add('Mocha')
            if 'pytest' in test_lower:
                test_frameworks.add('Pytest')
            if 'unittest' in test_lower:
                test_frameworks.add('unittest')
            if 'cypress' in test_lower:
                test_frameworks.add('Cypress')
            if 'selenium' in test_lower:
                test_frameworks.add('Selenium')

        # Check config files
        for config_file in config_files:
            config_lower = config_file.lower()
            if 'jest.config' in config_lower:
                test_frameworks.add('Jest')
            if 'cypress.json' in config_lower:
                test_frameworks.add('Cypress')

        return list(test_frameworks)

    def _detect_deployment_configs(self, project_files: Dict[str, Any]) -> List[str]:
        """Detect deployment configurations."""
        deployment_configs = set()

        build_files = [f.lower() for f in project_files.get('build_files', [])]
        config_files = [f.lower() for f in project_files.get('config_files', [])]

        # CI/CD configurations
        if '.github' in build_files:
            deployment_configs.add('GitHub Actions')
        if '.gitlab-ci.yml' in build_files:
            deployment_configs.add('GitLab CI')
        if 'jenkinsfile' in build_files:
            deployment_configs.add('Jenkins')
        if 'dockerfile' in build_files:
            deployment_configs.add('Docker')
        if 'docker-compose.yml' in build_files:
            deployment_configs.add('Docker Compose')
        if 'vercel.json' in config_files:
            deployment_configs.add('Vercel')
        if 'netlify.toml' in config_files:
            deployment_configs.add('Netlify')

        return list(deployment_configs)

    def _find_documentation(self, project_files: Dict[str, Any]) -> List[str]:
        """Find documentation files."""
        return project_files.get('documentation_files', [])

    def _analyze_project_health(self, analysis: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """Analyze project health and provide recommendations."""
        issues = []
        recommendations = []

        # Check for missing essential files
        if not analysis.get('documentation'):
            issues.append("Missing README or documentation")
            recommendations.append("Add a comprehensive README.md file")

        # Check for testing
        if not analysis.get('test_frameworks'):
            issues.append("No testing framework detected")
            recommendations.append("Add unit tests and choose a testing framework")

        # Check for dependency management
        if not analysis.get('dependencies', {}).get('package_managers'):
            issues.append("No package manager configuration found")
            recommendations.append("Add proper dependency management (package.json, requirements.txt, etc.)")

        # Check for build tools
        if not analysis.get('build_tools') and analysis.get('project_type') != 'unknown':
            recommendations.append("Consider adding build tools for optimization")

        # Check for deployment configuration
        if not analysis.get('deployment_configs'):
            recommendations.append("Add deployment configuration (Docker, CI/CD)")

        # Language-specific recommendations
        languages = analysis.get('languages', [])
        if 'JavaScript' in languages or 'TypeScript' in languages:
            if 'package.json' not in [f.lower() for f in analysis.get('structure', {}).get('config_files', [])]:
                issues.append("JavaScript/TypeScript project missing package.json")

        if 'Python' in languages:
            config_files = [f.lower() for f in analysis.get('structure', {}).get('config_files', [])]
            if not any(f in config_files for f in ['requirements.txt', 'pipfile', 'poetry.lock']):
                issues.append("Python project missing dependency file")

        return issues, recommendations

    def run_comprehensive_tests(self, test_type: str = "all") -> Dict[str, Any]:
        """
        Run comprehensive tests on the AI agent system.

        Args:
            test_type: Type of tests to run ('all', 'tools', 'integration', 'performance')

        Returns:
            Comprehensive test results
        """
        test_results = {
            'timestamp': time.time(),
            'test_type': test_type,
            'total_tests': 0,
            'passed_tests': 0,
            'failed_tests': 0,
            'skipped_tests': 0,
            'test_details': {},
            'performance_metrics': {},
            'recommendations': []
        }

        try:
            if test_type in ['all', 'tools']:
                tool_results = self._test_all_tools()
                test_results['test_details']['tools'] = tool_results
                test_results['total_tests'] += tool_results['total']
                test_results['passed_tests'] += tool_results['passed']
                test_results['failed_tests'] += tool_results['failed']

            if test_type in ['all', 'integration']:
                integration_results = self._test_integration()
                test_results['test_details']['integration'] = integration_results
                test_results['total_tests'] += integration_results['total']
                test_results['passed_tests'] += integration_results['passed']
                test_results['failed_tests'] += integration_results['failed']

            if test_type in ['all', 'performance']:
                performance_results = self._test_performance()
                test_results['test_details']['performance'] = performance_results
                test_results['performance_metrics'] = performance_results.get('metrics', {})
                test_results['total_tests'] += performance_results['total']
                test_results['passed_tests'] += performance_results['passed']
                test_results['failed_tests'] += performance_results['failed']

            # Generate recommendations based on test results
            test_results['recommendations'] = self._generate_test_recommendations(test_results)

            logger.info(f"Comprehensive testing completed: {test_results['passed_tests']}/{test_results['total_tests']} tests passed")
            return test_results

        except Exception as e:
            logger.error(f"Comprehensive testing failed: {e}")
            test_results['error'] = str(e)
            return test_results

    def _test_all_tools(self) -> Dict[str, Any]:
        """Test all available tools."""
        tool_results = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'details': {}
        }

        # Test each tool
        tools_to_test = [
            ('shell', 'echo "test"'),
            ('file', 'list .'),
            ('web', 'search test query'),
            ('codebase', 'find *.py'),
        ]

        for tool_name, test_command in tools_to_test:
            tool_results['total'] += 1
            try:
                if tool_name in self.tools:
                    result = self.tools[tool_name](test_command)
                    if result and not result.startswith('Error'):
                        tool_results['passed'] += 1
                        tool_results['details'][tool_name] = {'status': 'passed', 'result': result[:100]}
                    else:
                        tool_results['failed'] += 1
                        tool_results['details'][tool_name] = {'status': 'failed', 'error': result}
                else:
                    tool_results['failed'] += 1
                    tool_results['details'][tool_name] = {'status': 'failed', 'error': 'Tool not found'}
            except Exception as e:
                tool_results['failed'] += 1
                tool_results['details'][tool_name] = {'status': 'failed', 'error': str(e)}

        return tool_results

    def _test_integration(self) -> Dict[str, Any]:
        """Test integration between different components."""
        integration_results = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'details': {}
        }

        # Test conversation management
        integration_results['total'] += 1
        try:
            if self.conversation_manager.current_conversation is None:
                self.conversation_manager.new_conversation()
            integration_results['passed'] += 1
            integration_results['details']['conversation_management'] = {'status': 'passed'}
        except Exception as e:
            integration_results['failed'] += 1
            integration_results['details']['conversation_management'] = {'status': 'failed', 'error': str(e)}

        # Test model integration
        integration_results['total'] += 1
        try:
            if self.model_manager and hasattr(self.model_manager, 'current_model'):
                integration_results['passed'] += 1
                integration_results['details']['model_integration'] = {'status': 'passed'}
            else:
                integration_results['failed'] += 1
                integration_results['details']['model_integration'] = {'status': 'failed', 'error': 'Model manager not properly initialized'}
        except Exception as e:
            integration_results['failed'] += 1
            integration_results['details']['model_integration'] = {'status': 'failed', 'error': str(e)}

        # Test iterative execution
        integration_results['total'] += 1
        try:
            if hasattr(self, 'iterative_mode'):
                integration_results['passed'] += 1
                integration_results['details']['iterative_execution'] = {'status': 'passed'}
            else:
                integration_results['failed'] += 1
                integration_results['details']['iterative_execution'] = {'status': 'failed', 'error': 'Iterative mode not available'}
        except Exception as e:
            integration_results['failed'] += 1
            integration_results['details']['iterative_execution'] = {'status': 'failed', 'error': str(e)}

        # Test advanced features
        integration_results['total'] += 1
        try:
            if hasattr(self, 'cache_system') and hasattr(self, 'performance_analyzer'):
                integration_results['passed'] += 1
                integration_results['details']['advanced_features'] = {'status': 'passed'}
            else:
                integration_results['failed'] += 1
                integration_results['details']['advanced_features'] = {'status': 'failed', 'error': 'Advanced features not properly initialized'}
        except Exception as e:
            integration_results['failed'] += 1
            integration_results['details']['advanced_features'] = {'status': 'failed', 'error': str(e)}

        return integration_results

    def _test_performance(self) -> Dict[str, Any]:
        """Test performance characteristics."""
        performance_results = {
            'total': 0,
            'passed': 0,
            'failed': 0,
            'details': {},
            'metrics': {}
        }

        # Test response time
        performance_results['total'] += 1
        try:
            start_time = time.time()
            self.process_message("Hello, this is a test message.")
            response_time = time.time() - start_time

            performance_results['metrics']['response_time'] = response_time
            if response_time < 10.0:  # Should respond within 10 seconds
                performance_results['passed'] += 1
                performance_results['details']['response_time'] = {'status': 'passed', 'time': response_time}
            else:
                performance_results['failed'] += 1
                performance_results['details']['response_time'] = {'status': 'failed', 'time': response_time, 'threshold': 10.0}
        except Exception as e:
            performance_results['failed'] += 1
            performance_results['details']['response_time'] = {'status': 'failed', 'error': str(e)}

        # Test memory usage
        performance_results['total'] += 1
        try:
            memory_usage = self._get_memory_usage()
            performance_results['metrics']['memory_usage'] = memory_usage

            if memory_usage < 500:  # Should use less than 500MB
                performance_results['passed'] += 1
                performance_results['details']['memory_usage'] = {'status': 'passed', 'usage': memory_usage}
            else:
                performance_results['failed'] += 1
                performance_results['details']['memory_usage'] = {'status': 'failed', 'usage': memory_usage, 'threshold': 500}
        except Exception as e:
            performance_results['failed'] += 1
            performance_results['details']['memory_usage'] = {'status': 'failed', 'error': str(e)}

        # Test concurrent operations
        performance_results['total'] += 1
        try:
            if hasattr(self, 'thread_manager'):
                # Test thread manager
                executor_name = "test_executor"
                self.thread_manager.create_executor(executor_name, workers=2)

                # Submit test tasks
                task_futures = []
                for i in range(3):
                    future = self.thread_manager.submit_task(
                        executor_name,
                        f"test_task_{i}",
                        lambda x: f"Task {x} completed",
                        i
                    )
                    task_futures.append(future)

                # Wait for completion
                completed = 0
                for i in range(3):
                    try:
                        result = self.thread_manager.get_result(executor_name, f"test_task_{i}", timeout=5)
                        if result:
                            completed += 1
                    except:
                        pass

                self.thread_manager.shutdown_executor(executor_name)

                if completed >= 2:  # At least 2 out of 3 should complete
                    performance_results['passed'] += 1
                    performance_results['details']['concurrent_operations'] = {'status': 'passed', 'completed': completed}
                else:
                    performance_results['failed'] += 1
                    performance_results['details']['concurrent_operations'] = {'status': 'failed', 'completed': completed}
            else:
                performance_results['failed'] += 1
                performance_results['details']['concurrent_operations'] = {'status': 'failed', 'error': 'Thread manager not available'}
        except Exception as e:
            performance_results['failed'] += 1
            performance_results['details']['concurrent_operations'] = {'status': 'failed', 'error': str(e)}

        return performance_results

    def _generate_test_recommendations(self, test_results: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on test results."""
        recommendations = []

        # Overall test success rate
        total_tests = test_results.get('total_tests', 0)
        passed_tests = test_results.get('passed_tests', 0)

        if total_tests > 0:
            success_rate = passed_tests / total_tests
            if success_rate < 0.8:
                recommendations.append(f"Test success rate is {success_rate:.1%}. Consider investigating failed tests.")

        # Tool-specific recommendations
        tool_details = test_results.get('test_details', {}).get('tools', {}).get('details', {})
        for tool_name, tool_result in tool_details.items():
            if tool_result.get('status') == 'failed':
                recommendations.append(f"Tool '{tool_name}' is failing. Check tool configuration and dependencies.")

        # Performance recommendations
        performance_metrics = test_results.get('performance_metrics', {})
        if performance_metrics.get('response_time', 0) > 5:
            recommendations.append("Response time is slow. Consider optimizing model or caching.")

        if performance_metrics.get('memory_usage', 0) > 300:
            recommendations.append("High memory usage detected. Consider memory optimization.")

        # Integration recommendations
        integration_details = test_results.get('test_details', {}).get('integration', {}).get('details', {})
        for component, result in integration_details.items():
            if result.get('status') == 'failed':
                recommendations.append(f"Integration issue with {component}. Check component initialization.")

        if not recommendations:
            recommendations.append("All tests passed successfully! The system is functioning well.")

        return recommendations

    def validate_system_health(self) -> Dict[str, Any]:
        """Validate overall system health."""
        health_check = {
            'timestamp': time.time(),
            'overall_status': 'healthy',
            'components': {},
            'warnings': [],
            'errors': [],
            'recommendations': []
        }

        try:
            # Check core components
            components_to_check = [
                ('model_manager', self.model_manager),
                ('conversation_manager', self.conversation_manager),
                ('tool_manager', self.tool_manager),
                ('cache_system', getattr(self, 'cache_system', None)),
                ('performance_analyzer', getattr(self, 'performance_analyzer', None)),
                ('thread_manager', getattr(self, 'thread_manager', None))
            ]

            for component_name, component in components_to_check:
                if component is not None:
                    health_check['components'][component_name] = 'healthy'
                else:
                    health_check['components'][component_name] = 'missing'
                    health_check['warnings'].append(f"{component_name} is not initialized")

            # Check workspace directory
            if os.path.exists(self.workspace_dir):
                health_check['components']['workspace'] = 'healthy'
            else:
                health_check['components']['workspace'] = 'error'
                health_check['errors'].append(f"Workspace directory not found: {self.workspace_dir}")

            # Check tools availability
            available_tools = len(self.tools) if hasattr(self, 'tools') else 0
            if available_tools > 5:
                health_check['components']['tools'] = 'healthy'
            elif available_tools > 0:
                health_check['components']['tools'] = 'partial'
                health_check['warnings'].append(f"Only {available_tools} tools available")
            else:
                health_check['components']['tools'] = 'error'
                health_check['errors'].append("No tools available")

            # Determine overall status
            if health_check['errors']:
                health_check['overall_status'] = 'unhealthy'
            elif health_check['warnings']:
                health_check['overall_status'] = 'degraded'

            # Generate recommendations
            if health_check['warnings']:
                health_check['recommendations'].append("Address warning conditions to improve system reliability")
            if health_check['errors']:
                health_check['recommendations'].append("Fix error conditions immediately to restore full functionality")
            if health_check['overall_status'] == 'healthy':
                health_check['recommendations'].append("System is healthy and operating normally")

            return health_check

        except Exception as e:
            health_check['overall_status'] = 'error'
            health_check['errors'].append(f"Health check failed: {str(e)}")
            return health_check
